# DOCUMENTACIÓN DE BUENAS PRÁCTICAS - SPA PASCALE

## Información del Documento
**Proyecto:** SPA Pascale - Bienestar Integral  
**Fecha de creación:** 25 de Julio de 2025  
**Versión:** 1.0  
**Responsable:** Agente de IA Senior  

---

## 1. PRINCIPIOS FUNDAMENTALES

### 1.1 Principios de Desarrollo
- **Código <PERSON>:** Escribir código legible y mantenible
- **Separación de Responsabilidades:** Cada componente tiene una función específica
- **DRY (Don't Repeat Yourself):** Evitar duplicación de código
- **SOLID Principles:** Aplicar principios de diseño orientado a objetos
- **Performance First:** Optimizar desde el inicio, no como afterthought

### 1.2 Principios de Seguridad
- **Security by Design:** Implementar seguridad desde el diseño
- **Least Privilege:** Acceso mínimo necesario
- **Defense in Depth:** Múltiples capas de seguridad
- **Fail Secure:** Fallar de manera segura
- **Input Validation:** Validar todas las entradas

## 2. ESTÁNDARES DE CÓDIGO

### 2.1 Convenciones de Nomenclatura

#### Componentes React
```typescript
// ✅ Correcto: PascalCase para componentes
export const TreatmentCard: React.FC<TreatmentCardProps> = ({ treatment }) => {
  return <div>{treatment.title}</div>;
};

// ❌ Incorrecto: camelCase para componentes
export const treatmentCard = () => { /* ... */ };
```

#### Variables y Funciones
```typescript
// ✅ Correcto: camelCase para variables y funciones
const isEmailConfigured = true;
const handleFormSubmit = () => { /* ... */ };

// ❌ Incorrecto: PascalCase para variables
const IsEmailConfigured = true;
```

#### Constantes
```typescript
// ✅ Correcto: UPPER_SNAKE_CASE para constantes
const SLIDE_INTERVAL = 7000;
const WHATSAPP_PHONE_NUMBER = '56977509997';

// ❌ Incorrecto: camelCase para constantes
const slideInterval = 7000;
```

#### Tipos e Interfaces
```typescript
// ✅ Correcto: PascalCase para tipos
interface TreatmentCardProps {
  treatment: Treatment;
  onSelect?: (id: string) => void;
}

// ✅ Correcto: Sufijo Props para props de componentes
interface NavbarProps {
  isOpen: boolean;
}
```

### 2.2 Estructura de Archivos

#### Organización de Imports
```typescript
// ✅ Correcto: Orden de imports
// 1. React y librerías externas
import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';

// 2. Componentes internos
import { OptimizedImage } from './OptimizedImage';
import { LoadingSpinner } from './LoadingSpinner';

// 3. Hooks y servicios
import { useImagePreloader } from '../hooks/useImagePreloader';
import { sendContactEmail } from '../services/emailService';

// 4. Tipos y constantes
import { Treatment, Testimonial } from '../types';
import { TREATMENTS_DATA } from '../constants';
```

#### Estructura de Componentes
```typescript
// ✅ Estructura recomendada para componentes
import React, { useState, useEffect } from 'react';
import { ComponentProps } from '../types';

// Interfaces locales (si las hay)
interface LocalState {
  isLoading: boolean;
}

// Constantes locales
const DEFAULT_TIMEOUT = 5000;

// Componente principal
export const ComponentName: React.FC<ComponentProps> = ({ 
  prop1, 
  prop2,
  onAction 
}) => {
  // 1. Hooks de estado
  const [localState, setLocalState] = useState<LocalState>({
    isLoading: false
  });

  // 2. Hooks personalizados
  const { data, isLoading } = useCustomHook();

  // 3. Efectos
  useEffect(() => {
    // Lógica de efecto
  }, []);

  // 4. Handlers
  const handleClick = () => {
    // Lógica del handler
  };

  // 5. Render condicional temprano
  if (isLoading) {
    return <LoadingSpinner />;
  }

  // 6. JSX principal
  return (
    <div className="component-container">
      {/* Contenido */}
    </div>
  );
};
```

### 2.3 Gestión de Estado

#### useState Best Practices
```typescript
// ✅ Correcto: Estado específico y tipado
const [isLoading, setIsLoading] = useState<boolean>(false);
const [error, setError] = useState<string | null>(null);
const [formData, setFormData] = useState<ContactFormData>({
  name: '',
  email: '',
  message: ''
});

// ❌ Incorrecto: Estado genérico sin tipos
const [state, setState] = useState({});
```

#### useEffect Best Practices
```typescript
// ✅ Correcto: Dependencias específicas y cleanup
useEffect(() => {
  const timer = setInterval(() => {
    setCurrentSlide(prev => (prev + 1) % images.length);
  }, SLIDE_INTERVAL);

  return () => clearInterval(timer);
}, [images.length]);

// ❌ Incorrecto: Sin dependencias o cleanup
useEffect(() => {
  setInterval(() => {
    setCurrentSlide(prev => prev + 1);
  }, 5000);
});
```

## 3. PRÁCTICAS DE RENDIMIENTO

### 3.1 Optimización de Componentes

#### Lazy Loading
```typescript
// ✅ Implementar lazy loading para imágenes
const OptimizedImage: React.FC<ImageProps> = ({ src, alt, ...props }) => {
  return (
    <img
      src={src}
      alt={alt}
      loading="lazy"
      {...props}
    />
  );
};
```

#### Memoización Selectiva
```typescript
// ✅ Usar React.memo para componentes puros
export const TreatmentCard = React.memo<TreatmentCardProps>(({ treatment }) => {
  return (
    <div className="treatment-card">
      <h3>{treatment.title}</h3>
      <p>{treatment.description}</p>
    </div>
  );
});
```

### 3.2 Optimización de Assets

#### Imágenes
- Usar formatos modernos (WebP, AVIF) cuando sea posible
- Implementar lazy loading para imágenes no críticas
- Precargar imágenes críticas del hero section
- Usar dimensiones apropiadas para cada breakpoint

#### JavaScript
- Code splitting automático con Vite
- Eliminar código no utilizado (tree shaking)
- Minificación en producción
- Chunks separados para librerías principales

## 4. PRÁCTICAS DE SEGURIDAD

### 4.1 Gestión de Datos Sensibles

#### Variables de Entorno
```typescript
// ✅ Correcto: Variables de entorno con fallbacks
const EMAILJS_SERVICE_ID = import.meta.env.VITE_EMAILJS_SERVICE_ID || 'default_value';

// Validación de configuración
export const isEmailJSConfigured = (): boolean => {
  return !!(
    EMAILJS_SERVICE_ID && 
    EMAILJS_SERVICE_ID !== 'default_value'
  );
};
```

#### Validación de Formularios
```typescript
// ✅ Implementar validación robusta
const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

const validateForm = (formData: ContactFormData): string[] => {
  const errors: string[] = [];
  
  if (!formData.name.trim()) {
    errors.push('El nombre es requerido');
  }
  
  if (!validateEmail(formData.email)) {
    errors.push('Email inválido');
  }
  
  return errors;
};
```

### 4.2 Manejo de Errores

#### Error Boundaries
```typescript
// ✅ Implementar manejo de errores robusto
try {
  const response = await emailjs.send(serviceId, templateId, params);
  return { success: true, message: 'Enviado exitosamente' };
} catch (error) {
  console.error('Error sending email:', error);
  return sendFallbackMethod(params);
}
```

## 5. PRÁCTICAS DE ACCESIBILIDAD

### 5.1 Estructura Semántica

#### HTML Semántico
```typescript
// ✅ Correcto: Usar elementos semánticos
<main className="main-content">
  <section aria-labelledby="treatments-heading">
    <h2 id="treatments-heading">Nuestros Tratamientos</h2>
    <article className="treatment-card">
      <h3>Masaje de Relajación</h3>
      <p>Descripción del tratamiento...</p>
    </article>
  </section>
</main>

// ❌ Incorrecto: Solo divs
<div className="main-content">
  <div className="treatments">
    <div className="treatment-card">
      <div>Masaje de Relajación</div>
    </div>
  </div>
</div>
```

#### ARIA Labels
```typescript
// ✅ Implementar ARIA labels apropiados
<button
  aria-label="Abrir menú de navegación"
  aria-expanded={isMenuOpen}
  onClick={toggleMenu}
>
  <span className="hamburger-icon" aria-hidden="true" />
</button>

<img
  src={image.src}
  alt={image.alt}
  role="img"
  aria-describedby={`description-${image.id}`}
/>
```

### 5.2 Navegación por Teclado

#### Focus Management
```typescript
// ✅ Gestión apropiada del foco
const handleKeyDown = (event: KeyboardEvent) => {
  if (event.key === 'Escape') {
    closeModal();
    previousFocusRef.current?.focus();
  }
};

useEffect(() => {
  if (isModalOpen) {
    modalRef.current?.focus();
  }
}, [isModalOpen]);
```

## 6. PRÁCTICAS DE TESTING

### 6.1 Testing Manual

#### Checklist de Testing
- [ ] Funcionalidad en diferentes navegadores
- [ ] Responsive design en múltiples dispositivos
- [ ] Navegación por teclado completa
- [ ] Formularios funcionando correctamente
- [ ] Carga de imágenes optimizada
- [ ] Performance dentro de métricas objetivo

### 6.2 Testing Automatizado (Futuro)

#### Estructura de Tests
```typescript
// ✅ Estructura recomendada para tests
describe('TreatmentCard Component', () => {
  it('should render treatment information correctly', () => {
    // Test implementation
  });

  it('should handle click events properly', () => {
    // Test implementation
  });

  it('should be accessible via keyboard navigation', () => {
    // Test implementation
  });
});
```

## 7. PRÁCTICAS DE DOCUMENTACIÓN

### 7.1 Comentarios en Código

#### Comentarios Útiles
```typescript
// ✅ Comentarios que explican el "por qué"
// Preload critical images to improve perceived performance
const { isLoading } = useSpaImagePreloader();

// Fallback to mailto if EmailJS is not configured
if (!isEmailJSConfigured()) {
  return sendMailtoFallback(formData);
}

// ❌ Comentarios que explican el "qué" (obvio)
// Set loading to true
setIsLoading(true);
```

#### JSDoc para Funciones Complejas
```typescript
/**
 * Sends contact form data via EmailJS with fallback to mailto
 * @param formData - Contact form data including name, email, and message
 * @returns Promise with success status and message
 */
export const sendContactEmail = async (
  formData: ContactFormData
): Promise<EmailResponse> => {
  // Implementation
};
```

### 7.2 README y Documentación

#### Mantener Documentación Actualizada
- Actualizar README.md con cada cambio significativo
- Documentar nuevas funcionalidades en docs/funcionalidades.md
- Registrar cambios en docs/cambios.md
- Mantener docs/dependencias.md actualizado

## 8. PRÁCTICAS DE DEPLOYMENT

### 8.1 Pre-Deployment Checklist

#### Verificaciones Obligatorias
```bash
# Verificar tipos
npm run type-check

# Verificar linting
npm run lint

# Verificar build
npm run build

# Verificar formato
npm run format:check
```

#### Variables de Entorno
- Configurar variables en plataforma de hosting
- Verificar que todas las variables requeridas están presentes
- Probar funcionalidades que dependen de variables

### 8.2 Post-Deployment

#### Verificaciones Post-Deploy
- [ ] Sitio carga correctamente
- [ ] Formularios funcionan
- [ ] Navegación operativa
- [ ] Imágenes cargan apropiadamente
- [ ] WhatsApp integration funcional
- [ ] Performance dentro de métricas objetivo

## 9. MANTENIMIENTO CONTINUO

### 9.1 Actualizaciones Regulares

#### Dependencias
- Revisar actualizaciones semanalmente
- Actualizar dependencias de desarrollo mensualmente
- Actualizar dependencias de producción trimestralmente
- Ejecutar `npm audit` antes de cada deployment

#### Código
- Refactorizar código duplicado
- Optimizar componentes con bajo rendimiento
- Actualizar documentación con cambios
- Revisar y limpiar código no utilizado

### 9.2 Monitoreo

#### Métricas a Monitorear
- Performance (Core Web Vitals)
- Error rates
- User engagement
- Conversion rates (formularios)

---

**Última actualización:** 25 de Julio de 2025  
**Próxima revisión:** 1 de Agosto de 2025  
**Responsable:** Agente de IA Senior
