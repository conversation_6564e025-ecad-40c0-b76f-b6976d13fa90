'use client';



import React, { useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { useScrollAnimation } from '../../hooks/useScrollAnimation';
import { sendContactEmail, isEmailJSConfigured } from '../../services/emailService';
import { LoadingSpinner } from '../../components/LoadingSpinner';



interface FormData {
  name: string;
  email: string;
  phone: string;
  service: string;
  message: string;
}

export default function ContactPage() {
  const searchParams = useSearchParams();
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    phone: '',
    service: '',
    message: '',
  });
  const [errors, setErrors] = useState<Partial<FormData>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<{
    type: 'success' | 'error' | null;
    message: string;
  }>({ type: null, message: '' });
  const [sectionRef, isVisible] = useScrollAnimation<HTMLDivElement>({
    threshold: 0.05,
  });

  useEffect(() => {
    const selectedService = searchParams.get('service');
    if (selectedService) {
      setFormData(prevData => ({
        ...prevData,
        service: selectedService,
      }));
    }
  }, [searchParams]);

  const validateForm = (): boolean => {
    const newErrors: Partial<FormData> = {};
    if (!formData.name.trim()) newErrors.name = 'El nombre es obligatorio.';
    if (!formData.email.trim()) {
      newErrors.email = 'El correo electrónico es obligatorio.';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'El correo electrónico no es válido.';
    }
    if (formData.phone.trim() && !/^\+?[0-9\s-]{7,15}$/.test(formData.phone)) {
      newErrors.phone = 'El número de teléfono no es válido.';
    }
    if (!formData.service)
      newErrors.service = 'Selecciona un servicio de interés.';
    if (!formData.message.trim())
      newErrors.message = 'El mensaje es obligatorio.';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
    if (errors[e.target.name as keyof FormData]) {
      setErrors({
        ...errors,
        [e.target.name]: undefined,
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!validateForm() || isSubmitting) {
      return;
    }

    setIsSubmitting(true);
    setSubmitStatus({ type: null, message: '' });

    try {
      const result = await sendContactEmail({
        name: formData.name,
        email: formData.email,
        phone: formData.phone,
        service: formData.service,
        message: formData.message,
      });

      if (result.success) {
        setSubmitStatus({
          type: 'success',
          message: result.message,
        });
        // Limpiar formulario solo si el envío fue exitoso
        setFormData({ name: '', email: '', phone: '', service: '', message: '' });
        setErrors({});
      } else {
        setSubmitStatus({
          type: 'error',
          message: result.error || 'Error al enviar el mensaje. Inténtalo nuevamente.',
        });
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      setSubmitStatus({
        type: 'error',
        message: 'Error inesperado. Por favor, inténtalo nuevamente.',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div
      ref={sectionRef}
      className={`py-16 sm:py-24 bg-brand-light transition-all duration-700 ease-out transform ${
        isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
      }`}
    >
      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-serif font-bold text-brand-primary sm:text-5xl">
            Contacto y Agendamiento
          </h1>
          <p className="mt-4 text-lg text-brand-text-light max-w-2xl mx-auto">
            ¿Lista para tu momento de bienestar? Completa el formulario o
            contáctame directamente.
          </p>
        </div>

        <div className="bg-white p-8 sm:p-12 rounded-lg shadow-xl">
          <form onSubmit={handleSubmit} className="space-y-6" noValidate>
            <div>
              <label
                htmlFor="name"
                className="block text-sm font-medium text-brand-text"
              >
                Nombre Completo
              </label>
              <input
                type="text"
                name="name"
                id="name"
                value={formData.name}
                onChange={handleChange}
                required
                aria-required="true"
                aria-invalid={!!errors.name}
                aria-describedby={errors.name ? 'name-error' : undefined}
                className={`mt-1 block w-full px-3 py-2 bg-brand-light border rounded-md shadow-sm focus:outline-none focus:ring-brand-primary focus:border-brand-primary sm:text-sm placeholder-slate-500 ${errors.name ? 'border-red-500' : 'border-slate-300'}`}
                placeholder="Ej: Ana Pérez"
              />
              {errors.name && (
                <p id="name-error" className="mt-1 text-xs text-red-600">
                  {errors.name}
                </p>
              )}
            </div>
            <div>
              <label
                htmlFor="email"
                className="block text-sm font-medium text-brand-text"
              >
                Correo Electrónico
              </label>
              <input
                type="email"
                name="email"
                id="email"
                value={formData.email}
                onChange={handleChange}
                required
                aria-required="true"
                aria-invalid={!!errors.email}
                aria-describedby={errors.email ? 'email-error' : undefined}
                className={`mt-1 block w-full px-3 py-2 bg-brand-light border rounded-md shadow-sm focus:outline-none focus:ring-brand-primary focus:border-brand-primary sm:text-sm placeholder-slate-500 ${errors.email ? 'border-red-500' : 'border-slate-300'}`}
                placeholder="Ej: <EMAIL>"
              />
              {errors.email && (
                <p id="email-error" className="mt-1 text-xs text-red-600">
                  {errors.email}
                </p>
              )}
            </div>
            <div>
              <label
                htmlFor="phone"
                className="block text-sm font-medium text-brand-text"
              >
                Teléfono (Opcional)
              </label>
              <input
                type="tel"
                name="phone"
                id="phone"
                value={formData.phone}
                onChange={handleChange}
                aria-invalid={!!errors.phone}
                aria-describedby={errors.phone ? 'phone-error' : undefined}
                className={`mt-1 block w-full px-3 py-2 bg-brand-light border rounded-md shadow-sm focus:outline-none focus:ring-brand-primary focus:border-brand-primary sm:text-sm placeholder-slate-500 ${errors.phone ? 'border-red-500' : 'border-slate-300'}`}
                placeholder="Ej: +56 9 1234 5678"
              />
              {errors.phone && (
                <p id="phone-error" className="mt-1 text-xs text-red-600">
                  {errors.phone}
                </p>
              )}
            </div>
            <div>
              <label
                htmlFor="service"
                className="block text-sm font-medium text-brand-text"
              >
                Servicio de Interés
              </label>
              <select
                name="service"
                id="service"
                value={formData.service}
                onChange={handleChange}
                required
                aria-required="true"
                aria-invalid={!!errors.service}
                aria-describedby={errors.service ? 'service-error' : undefined}
                className={`mt-1 block w-full px-3 py-2 bg-brand-light border rounded-md shadow-sm focus:outline-none focus:ring-brand-primary focus:border-brand-primary sm:text-sm ${errors.service ? 'border-red-500' : 'border-slate-300'}`}
              >
                <option value="">Selecciona un servicio...</option>
                <option value="Masaje de Relajación Profunda">
                  Masaje de Relajación Profunda
                </option>
                <option value="Masaje Descontracturante Terapéutico">
                  Masaje Descontracturante Terapéutico
                </option>
                <option value="Drenaje Linfático Manual">
                  Drenaje Linfático Manual
                </option>
                <option value="Consulta General">Consulta General</option>
              </select>
              {errors.service && (
                <p id="service-error" className="mt-1 text-xs text-red-600">
                  {errors.service}
                </p>
              )}
            </div>
            <div>
              <label
                htmlFor="message"
                className="block text-sm font-medium text-brand-text"
              >
                Mensaje
              </label>
              <textarea
                name="message"
                id="message"
                rows={4}
                value={formData.message}
                onChange={handleChange}
                required
                aria-required="true"
                aria-invalid={!!errors.message}
                aria-describedby={errors.message ? 'message-error' : undefined}
                className={`mt-1 block w-full px-3 py-2 bg-brand-light border rounded-md shadow-sm focus:outline-none focus:ring-brand-primary focus:border-brand-primary sm:text-sm placeholder-slate-500 ${errors.message ? 'border-red-500' : 'border-slate-300'}`}
                placeholder="Escribe aquí tu consulta o el motivo de tu contacto..."
              ></textarea>
              {errors.message && (
                <p id="message-error" className="mt-1 text-xs text-red-600">
                  {errors.message}
                </p>
              )}
            </div>
            {/* Estado del envío */}
            {submitStatus.type && (
              <div
                className={`p-4 rounded-md ${
                  submitStatus.type === 'success'
                    ? 'bg-green-50 border border-green-200'
                    : 'bg-red-50 border border-red-200'
                }`}
              >
                <p
                  className={`text-sm ${
                    submitStatus.type === 'success'
                      ? 'text-green-800'
                      : 'text-red-800'
                  }`}
                >
                  {submitStatus.message}
                </p>
              </div>
            )}

            <div>
              <button
                type="submit"
                disabled={isSubmitting}
                className={`w-full flex justify-center items-center py-3 px-4 border border-transparent rounded-md shadow-md text-sm font-medium text-white transition-all duration-300 ease-in-out ${
                  isSubmitting
                    ? 'bg-slate-400 cursor-not-allowed'
                    : 'bg-brand-primary hover:bg-brand-dark hover:shadow-lg transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-primary'
                }`}
              >
                {isSubmitting ? (
                  <>
                    <LoadingSpinner size="sm" color="white" className="mr-2" />
                    Enviando...
                  </>
                ) : (
                  'Enviar Consulta'
                )}
              </button>

              {!isEmailJSConfigured() && (
                <p className="mt-3 text-xs text-brand-text-light text-center">
                  Se abrirá tu aplicación de correo electrónico para enviar el mensaje.
                </p>
              )}

              {isEmailJSConfigured() && (
                <p className="mt-3 text-xs text-brand-text-light text-center">
                  Tu mensaje será enviado directamente a nuestro email.
                </p>
              )}
            </div>
          </form>

          <div className="mt-10 text-center text-brand-text-light">
            <p className="text-lg font-semibold mb-2">
              También puedes contactarme por:
            </p>
            <p>
              <strong>Email:</strong>{' '}
              <a
                href="mailto:<EMAIL>"
                className="text-brand-primary hover:underline"
              >
                <EMAIL>
              </a>{' '}
            </p>
            {/* The Teléfono/WhatsApp line below has been removed */}
            {/* <p><strong>Teléfono/WhatsApp:</strong> <a href="tel:+56977509997" className="text-brand-primary hover:underline">+56 9 7750 9997</a> </p> */}
            <p className="mt-4 text-sm">
              Horario de atención para consultas: Lunes a Viernes de 9:00 a
              18:00 hrs.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}