# DOCUMENTACIÓN DE INTEGRACIÓN CONTINUA Y DESPLIEGUE - SPA PASCALE

## Información del Documento
**Proyecto:** SPA Pascale - Bienestar Integral  
**Fecha de creación:** 25 de Julio de 2025  
**Versión:** 1.0  
**Responsable:** Agente de IA Senior  

---

## 1. ESTRATEGIA DE CI/CD

### 1.1 Objetivos
- **Automatización completa:** <PERSON><PERSON> commit hasta producción
- **Calidad garantizada:** Testing y validación automática
- **Deployment seguro:** Rollback automático en caso de errores
- **Feedback rápido:** Notificaciones inmediatas de estado
- **Escalabilidad:** Pipeline que crece con el proyecto

### 1.2 Herramientas y Plataformas

#### Plataformas de Hosting
- **Netlify:** Plataforma principal recomendada
- **Vercel:** Alternativa configurada
- **GitHub Pages:** Backup option

#### Control de Versiones
- **Git:** Sistema de control de versiones
- **GitHub:** Repositorio principal y CI/CD
- **Branching Strategy:** GitFlow simplificado

## 2. CONFIGURACIÓN ACTUAL

### 2.1 Netlify Configuration

#### netlify.toml
```toml
[build]
  publish = "dist"
  command = "npm run build"

[build.environment]
  NODE_VERSION = "18"
  NPM_VERSION = "8"

# Redirects para SPA
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Headers de seguridad
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Strict-Transport-Security = "max-age=31536000; includeSubDomains"

# Cache optimization
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.js"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.css"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"
```

#### Variables de Entorno en Netlify
```bash
# EmailJS Configuration
VITE_EMAILJS_SERVICE_ID=service_xxxxxxx
VITE_EMAILJS_TEMPLATE_ID=template_xxxxxxx
VITE_EMAILJS_PUBLIC_KEY=xxxxxxxxxxxxxxx

# Build Configuration
NODE_VERSION=18
NPM_VERSION=8
```

### 2.2 Vercel Configuration

#### vercel.json
```json
{
  "buildCommand": "npm run build",
  "outputDirectory": "dist",
  "devCommand": "npm run dev",
  "installCommand": "npm install",
  "framework": "vite",
  "functions": {},
  "rewrites": [
    {
      "source": "/(.*)",
      "destination": "/index.html"
    }
  ],
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Frame-Options",
          "value": "DENY"
        },
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        },
        {
          "key": "X-XSS-Protection",
          "value": "1; mode=block"
        },
        {
          "key": "Referrer-Policy",
          "value": "strict-origin-when-cross-origin"
        }
      ]
    },
    {
      "source": "/assets/(.*)",
      "headers": [
        {
          "key": "Cache-Control",
          "value": "public, max-age=31536000, immutable"
        }
      ]
    }
  ]
}
```

## 3. PIPELINE DE CI/CD PLANIFICADO

### 3.1 GitHub Actions Workflow (Futuro v1.1.0)

#### .github/workflows/ci-cd.yml
```yaml
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  # Job 1: Quality Checks
  quality:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Type check
        run: npm run type-check

      - name: Lint check
        run: npm run lint

      - name: Format check
        run: npm run format:check

      - name: Security audit
        run: npm audit --audit-level=high

  # Job 2: Build and Test
  build-test:
    needs: quality
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build application
        run: npm run build
        env:
          VITE_EMAILJS_SERVICE_ID: ${{ secrets.VITE_EMAILJS_SERVICE_ID }}
          VITE_EMAILJS_TEMPLATE_ID: ${{ secrets.VITE_EMAILJS_TEMPLATE_ID }}
          VITE_EMAILJS_PUBLIC_KEY: ${{ secrets.VITE_EMAILJS_PUBLIC_KEY }}

      - name: Run tests
        run: npm run test

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: dist
          path: dist/

  # Job 3: Performance Testing
  performance:
    needs: build-test
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: dist
          path: dist/

      - name: Lighthouse CI
        uses: treosh/lighthouse-ci-action@v10
        with:
          configPath: './lighthouserc.json'
          uploadArtifacts: true

  # Job 4: Deploy to Staging
  deploy-staging:
    needs: [quality, build-test, performance]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    steps:
      - name: Deploy to Netlify Staging
        uses: netlify/actions/cli@master
        with:
          args: deploy --dir=dist --alias=staging
        env:
          NETLIFY_AUTH_TOKEN: ${{ secrets.NETLIFY_AUTH_TOKEN }}
          NETLIFY_SITE_ID: ${{ secrets.NETLIFY_SITE_ID }}

  # Job 5: Deploy to Production
  deploy-production:
    needs: [quality, build-test, performance]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - name: Deploy to Netlify Production
        uses: netlify/actions/cli@master
        with:
          args: deploy --dir=dist --prod
        env:
          NETLIFY_AUTH_TOKEN: ${{ secrets.NETLIFY_AUTH_TOKEN }}
          NETLIFY_SITE_ID: ${{ secrets.NETLIFY_SITE_ID }}

      - name: Notify deployment success
        uses: 8398a7/action-slack@v3
        with:
          status: success
          text: 'SPA Pascale deployed successfully to production!'
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
```

### 3.2 Lighthouse CI Configuration

#### lighthouserc.json
```json
{
  "ci": {
    "collect": {
      "staticDistDir": "./dist",
      "numberOfRuns": 3
    },
    "assert": {
      "assertions": {
        "categories:performance": ["error", {"minScore": 0.9}],
        "categories:accessibility": ["error", {"minScore": 0.95}],
        "categories:best-practices": ["error", {"minScore": 0.9}],
        "categories:seo": ["error", {"minScore": 0.9}]
      }
    },
    "upload": {
      "target": "temporary-public-storage"
    }
  }
}
```

## 4. ESTRATEGIA DE BRANCHING

### 4.1 GitFlow Simplificado

#### Ramas Principales
- **main:** Código en producción
- **develop:** Código en desarrollo
- **feature/*:** Nuevas funcionalidades
- **hotfix/*:** Correcciones urgentes

#### Flujo de Trabajo
```
feature/nueva-funcionalidad → develop → main
                                ↓
                           staging environment → production
```

### 4.2 Políticas de Merge

#### Requisitos para Merge a develop
- [ ] Todos los checks de CI pasan
- [ ] Code review aprobado
- [ ] Tests unitarios pasan
- [ ] No vulnerabilidades de seguridad

#### Requisitos para Merge a main
- [ ] Todos los checks de CI pasan
- [ ] Testing en staging exitoso
- [ ] Performance tests pasan
- [ ] Aprobación de stakeholder

## 5. DEPLOYMENT AUTOMÁTICO

### 5.1 Netlify Auto-Deploy

#### Configuración Actual
- **Trigger:** Push a rama main
- **Build Command:** `npm run build`
- **Publish Directory:** `dist`
- **Node Version:** 18
- **Deploy Previews:** Habilitado para PRs

#### Variables de Entorno
```bash
# Configuradas en Netlify Dashboard
VITE_EMAILJS_SERVICE_ID=service_xxxxxxx
VITE_EMAILJS_TEMPLATE_ID=template_xxxxxxx
VITE_EMAILJS_PUBLIC_KEY=xxxxxxxxxxxxxxx
NODE_VERSION=18
```

### 5.2 Vercel Auto-Deploy

#### Configuración
- **Framework:** Vite detectado automáticamente
- **Build Command:** `npm run build`
- **Output Directory:** `dist`
- **Install Command:** `npm install`

## 6. MONITOREO Y ALERTAS

### 6.1 Métricas de Deployment

#### Métricas Actuales
- **Build Time:** ~2-3 minutos
- **Deploy Time:** ~30 segundos
- **Success Rate:** 100% (inicial)
- **Rollback Time:** <1 minuto

#### Objetivos de Performance
- **Build Time:** <5 minutos
- **Deploy Time:** <1 minuto
- **Success Rate:** >99%
- **Rollback Time:** <30 segundos

### 6.2 Alertas y Notificaciones (Futuro)

#### Canales de Notificación
- **Slack:** Notificaciones de deployment
- **Email:** Alertas de fallos críticos
- **GitHub:** Status checks en PRs
- **Dashboard:** Métricas en tiempo real

#### Tipos de Alertas
- ✅ Deployment exitoso
- ❌ Build fallido
- ⚠️ Performance degradation
- 🔒 Security vulnerabilities
- 📊 Métricas fuera de rango

## 7. ROLLBACK Y RECOVERY

### 7.1 Estrategia de Rollback

#### Rollback Automático
- **Trigger:** Health checks fallando
- **Tiempo:** <30 segundos
- **Método:** Revert a último deployment exitoso

#### Rollback Manual
- **Netlify:** Un click en dashboard
- **Vercel:** Revert desde dashboard
- **Git:** Revert commit y re-deploy

### 7.2 Backup y Recovery

#### Backups Automáticos
- **Git History:** Historial completo en GitHub
- **Netlify:** Snapshots automáticos
- **Vercel:** Deployments históricos

#### Recovery Procedures
1. **Identificar issue:** Monitoreo automático
2. **Evaluar impacto:** Métricas y user feedback
3. **Decidir estrategia:** Rollback vs hotfix
4. **Ejecutar recovery:** Automático o manual
5. **Verificar solución:** Health checks
6. **Post-mortem:** Análisis y mejoras

## 8. SEGURIDAD EN CI/CD

### 8.1 Gestión de Secretos

#### GitHub Secrets
```bash
# Secrets configurados en GitHub
NETLIFY_AUTH_TOKEN=xxxxx
NETLIFY_SITE_ID=xxxxx
VITE_EMAILJS_SERVICE_ID=xxxxx
VITE_EMAILJS_TEMPLATE_ID=xxxxx
VITE_EMAILJS_PUBLIC_KEY=xxxxx
SLACK_WEBHOOK_URL=xxxxx
```

#### Principios de Seguridad
- **Least Privilege:** Acceso mínimo necesario
- **Rotation:** Rotación regular de tokens
- **Encryption:** Secretos encriptados en reposo
- **Audit:** Log de acceso a secretos

### 8.2 Security Scanning

#### Herramientas Integradas
- **npm audit:** Vulnerabilidades en dependencias
- **Snyk:** Security scanning avanzado
- **SAST:** Static Application Security Testing
- **Dependency Check:** Análisis de dependencias

## 9. OPTIMIZACIONES FUTURAS

### 9.1 Versión 1.1.0
- [ ] GitHub Actions workflow completo
- [ ] Testing automatizado integrado
- [ ] Lighthouse CI para performance
- [ ] Notificaciones Slack/Email

### 9.2 Versión 1.2.0
- [ ] Multi-environment deployments
- [ ] Blue-green deployments
- [ ] Canary releases
- [ ] Advanced monitoring y alerting

### 9.3 Versión 1.3.0
- [ ] Infrastructure as Code
- [ ] Container-based deployments
- [ ] Advanced security scanning
- [ ] Performance optimization automática

## 10. COMANDOS ÚTILES

### 10.1 Deployment Manual

#### Netlify
```bash
# Install Netlify CLI
npm install -g netlify-cli

# Login
netlify login

# Deploy to staging
npm run build
netlify deploy --dir=dist

# Deploy to production
netlify deploy --dir=dist --prod
```

#### Vercel
```bash
# Install Vercel CLI
npm install -g vercel

# Login
vercel login

# Deploy
npm run build
vercel --prod
```

### 10.2 Verificación Local

#### Pre-deployment Checks
```bash
# Type checking
npm run type-check

# Linting
npm run lint

# Build verification
npm run build

# Local preview
npm run preview

# Security audit
npm audit
```

---

**Última actualización:** 25 de Julio de 2025  
**Próxima revisión:** 1 de Agosto de 2025  
**Responsable de CI/CD:** Agente de IA Senior
