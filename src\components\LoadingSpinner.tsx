'use client';

'use client';

import React from 'react';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  color?: 'primary' | 'secondary' | 'white';
  text?: string;
  className?: string;
  fullScreen?: boolean;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  color = 'primary',
  text,
  className = '',
  fullScreen = false,
}) => {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-8 w-8',
    lg: 'h-12 w-12',
    xl: 'h-16 w-16',
  };

  const colorClasses = {
    primary: 'border-brand-primary',
    secondary: 'border-brand-secondary',
    white: 'border-white',
  };

  const containerClasses = fullScreen
    ? 'fixed inset-0 bg-brand-light bg-opacity-90 flex items-center justify-center z-50'
    : 'flex items-center justify-center';

  return (
    <div className={`${containerClasses} ${className}`}>
      <div className="text-center">
        {/* Spinner principal */}
        <div
          className={`
            ${sizeClasses[size]} 
            ${colorClasses[color]}
            border-4 border-t-transparent rounded-full animate-spin mx-auto
          `}
          role="status"
          aria-label="Cargando"
        />
        
        {/* Texto opcional */}
        {text && (
          <p className="mt-3 text-sm text-brand-text-light font-medium">
            {text}
          </p>
        )}
        
        {/* Spinner adicional para efecto visual */}
        {size === 'xl' && (
          <div
            className={`
              absolute ${sizeClasses[size]} 
              ${colorClasses[color]}
              border-2 border-opacity-30 rounded-full animate-pulse
            `}
          />
        )}
      </div>
    </div>
  );
};

// Componente específico para carga de página del spa
export const SpaLoadingScreen: React.FC<{ progress?: number }> = ({ 
  progress 
}) => {
  return (
    <div className="fixed inset-0 bg-gradient-to-br from-brand-light to-white flex items-center justify-center z-50">
      <div className="text-center max-w-sm mx-auto px-6">
        {/* Logo o icono del spa */}
        <div className="mb-6">
          <div className="w-20 h-20 mx-auto bg-gradient-to-br from-brand-primary to-brand-secondary rounded-full flex items-center justify-center">
            <svg
              className="w-10 h-10 text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
              />
            </svg>
          </div>
        </div>

        {/* Título */}
        <h2 className="text-2xl font-serif font-bold text-brand-dark mb-2">
          SPA Pascale
        </h2>
        <p className="text-brand-text-light mb-6">
          Preparando tu experiencia de bienestar...
        </p>

        {/* Spinner */}
        <LoadingSpinner size="lg" color="primary" />

        {/* Barra de progreso si se proporciona */}
        {typeof progress === 'number' && (
          <div className="mt-6">
            <div className="w-full bg-slate-200 rounded-full h-2">
              <div
                className="bg-gradient-to-r from-brand-primary to-brand-secondary h-2 rounded-full transition-all duration-300 ease-out"
                style={{ width: `${Math.min(100, Math.max(0, progress * 100))}%` }}
              />
            </div>
            <p className="text-xs text-brand-text-light mt-2">
              {Math.round(progress * 100)}% completado
            </p>
          </div>
        )}
      </div>
    </div>
  );
};
