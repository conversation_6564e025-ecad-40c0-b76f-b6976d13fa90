'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image'; // Import Next.js Image component
import { CAROUSEL_IMAGES } from '../constants';

const SLIDE_INTERVAL = 7000; // 7 seconds per slide
const HERO_TITLE = 'Bienestar Integral: Armonía para tu Cuerpo y Mente';

export const Hero: React.FC = () => {
  const [currentSlide, setCurrentSlide] = useState(0);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide(prevSlide => (prevSlide + 1) % CAROUSEL_IMAGES.length);
    }, SLIDE_INTERVAL);
    return () => clearInterval(timer);
  }, []);

  // handleScrollClick is still useful for smooth scrolling to sections on the same page
  const handleScrollClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();
    const targetId = e.currentTarget.href.split('#')[1];
    const targetElement = document.getElementById(targetId);
    if (targetElement) {
      targetElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  return (
    <section
      className="relative py-32 sm:py-48 overflow-hidden"
      aria-roledescription="carousel"
      aria-label="Imágenes inspiradoras de naturaleza y bienestar"
    >
      {/* Image Slides */}
      {CAROUSEL_IMAGES.map((image, index) => (
        <div
          key={image.id}
          className={`absolute inset-0 w-full h-full transition-opacity duration-1000 ease-in-out ${
            index === currentSlide ? 'opacity-100' : 'opacity-0'
          }`}
          aria-hidden={index !== currentSlide}
          role="group"
          aria-roledescription="slide"
          aria-label={`Imagen ${index + 1} de ${CAROUSEL_IMAGES.length}: ${image.alt}`}
        >
          <Image
            src={image.src}
            alt={image.alt}
            className="w-full h-full object-cover"
            priority={index === 0} // Prioritize loading the first image
            fill // Use fill to make the image cover the parent div
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
        </div>
      ))}

      {/* Dark Overlay */}
      <div className="absolute inset-0 bg-black opacity-50"></div>

      {/* Content */}
      <div className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-white z-10">
        <h1 className="text-4xl sm:text-5xl md:text-6xl font-serif font-bold mb-6 drop-shadow-lg">
          {HERO_TITLE.split(' ').map((word, index) => (
            <span
              key={index}
              className="inline-block opacity-0 animate-fade-in-up"
              style={{ animationDelay: `${index * 0.15}s` }}
            >
              {word}&nbsp;
            </span>
          ))}
        </h1>
        <p
          className="text-lg sm:text-xl mb-8 font-light drop-shadow-md max-w-2xl mx-auto opacity-0 animate-fade-in-up"
          style={{
            animationDelay: `${HERO_TITLE.split(' ').length * 0.15 + 0.2}s`,
          }}
        >
          Un refugio de paz y sanación donde la cosmetología y la masoterapia se
          unen para restaurar tu equilibrio interior.
        </p>
        <div
          className="flex justify-center opacity-0 animate-fade-in-up"
          style={{
            animationDelay: `${HERO_TITLE.split(' ').length * 0.15 + 0.4}s`,
          }}
        >
          <a
            href="#tratamientos"
            onClick={handleScrollClick}
            className="px-8 py-3 bg-brand-primary hover:bg-brand-dark text-white font-semibold rounded-lg shadow-lg transition-transform duration-300 ease-in-out transform hover:scale-105"
          >
            Nuestros Tratamientos
          </a>
        </div>
      </div>

      {/* Dot Indicators */}
      <div className="absolute bottom-8 left-0 right-0 flex justify-center space-x-2 z-10">
        {CAROUSEL_IMAGES.map((_, index) => (
          <button
            key={`dot-${index}`}
            onClick={() => setCurrentSlide(index)}
            aria-label={`Ir a la imagen ${index + 1}`}
            className={`w-3 h-3 rounded-full transition-colors duration-300
              ${currentSlide === index ? 'bg-white scale-125' : 'bg-white/50 hover:bg-white/75'}
            `}
          />
        ))}
      </div>
    </section>
  );
};
