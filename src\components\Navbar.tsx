'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { NAV_LINKS } from '../constants';
import { NavLinkInfo } from '../types';
import { MenuIcon } from './icons/MenuIcon';
import { CloseIcon } from './icons/CloseIcon';
import { SiteLogo } from './icons/SiteLogo';
import { DateTimeDisplay } from './DateTimeDisplay';

export const Navbar: React.FC = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const pathname = usePathname();

  useEffect(() => {
    // Close mobile menu on route change
    setIsMobileMenuOpen(false);
    // Ensure body scroll is enabled when menu closes
    document.body.style.overflow = 'auto';
  }, [pathname]);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
    if (!isMobileMenuOpen) {
      document.body.style.overflow = 'hidden'; // Disable scroll when mobile menu is open
    } else {
      document.body.style.overflow = 'auto'; // Enable scroll when mobile menu is closed
    }
  };

  // Effect to clean up body overflow style when component unmounts or menu state changes
  useEffect(() => {
    return () => {
      document.body.style.overflow = 'auto';
    };
  }, []);

  return (
    <header className="bg-white shadow-md sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-20">
          {/* Left section: Logo */}
          <div className="flex items-center">
            <div className="flex-shrink-0">
              {' '}
              {/* Logo container */}
              <Link
                href="/"
                className="flex items-center text-brand-primary hover:text-brand-dark transition-colors duration-300"
                aria-label="Página de inicio Bienestar Integral"
              >
                <SiteLogo className="h-14 w-auto" />
              </Link>
            </div>
          </div>

          {/* Desktop Navigation & DateTime */}
          <div className="hidden md:flex items-center">
            <nav className="flex space-x-6 items-center">
              {NAV_LINKS.map((link: NavLinkInfo) => (
                <Link
                  key={link.label}
                  href={link.path}
                  className={`
                    ${
                      link.isButton
                        ? 'px-4 py-2 text-sm font-medium text-white bg-brand-primary hover:bg-brand-dark rounded-md shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-300 ease-in-out'
                        : 'text-brand-text-light hover:text-brand-primary font-medium transform hover:scale-102 hover:shadow-sm transition-all duration-300 ease-in-out py-2 px-1 rounded'
                    }
                    ${pathname === link.path && !link.isButton ? 'text-brand-primary font-semibold' : ''}
                  `}
                >
                  {link.label}
                </Link>
              ))}
            </nav>
            <div className="ml-6 pl-6 border-l border-slate-200">
              <DateTimeDisplay />
            </div>
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden flex items-center">
            <button
              onClick={toggleMobileMenu}
              aria-label={
                isMobileMenuOpen
                  ? 'Cerrar menú de navegación'
                  : 'Abrir menú de navegación'
              }
              aria-expanded={isMobileMenuOpen}
              className="text-brand-primary hover:text-brand-dark focus:outline-none p-2"
            >
              {isMobileMenuOpen ? <CloseIcon /> : <MenuIcon />}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}

      <div
        className={`
        md:hidden fixed inset-0 z-40 transform transition-transform duration-300 ease-in-out
        ${isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full'}
        bg-white shadow-lg
      `}
      >
        <div className="flex justify-between items-center p-4 border-b border-slate-200">
          <Link
            href="/"
            className="flex items-center text-brand-primary"
            onClick={toggleMobileMenu}
            aria-label="Página de inicio Bienestar Integral"
          >
            <SiteLogo className="h-12 w-auto" />{' '}
            {/* Adjusted size for mobile */}
          </Link>
          <button
            onClick={toggleMobileMenu}
            aria-label="Cerrar menú de navegación"
            className="text-brand-primary hover:text-brand-dark focus:outline-none p-2"
          >
            <CloseIcon />
          </button>
        </div>
        <nav className="flex flex-col space-y-3 px-4 pt-4 pb-4">
          {NAV_LINKS.map((link: NavLinkInfo) => (
            <Link
              key={link.label}
              href={link.path}
              onClick={toggleMobileMenu}
              className={`
                block px-3 py-3 rounded-md text-base font-medium
                ${
                  link.isButton
                    ? 'text-white bg-brand-primary hover:bg-brand-dark text-center shadow-sm hover:shadow-md transform hover:scale-102 transition-all duration-300 ease-in-out'
                    : 'text-brand-text-light hover:text-brand-primary hover:bg-teal-100 hover:shadow-sm transform hover:scale-102 transition-all duration-300 ease-in-out'
                }
                ${pathname === link.path && !link.isButton ? 'text-brand-primary bg-teal-100 font-semibold' : ''}
              `}
            >
              {link.label}
            </Link>
          ))}
        </nav>
      </div>
      {/* Overlay for when mobile menu is open */}
      {isMobileMenuOpen && (
        <div
          className="md:hidden fixed inset-0 bg-black opacity-30 z-30"
          onClick={toggleMobileMenu}
          aria-hidden="true"
        ></div>
      )}
    </header>
  );
};