# SPA Pascale - Bienestar Integral

Landing page profesional para el centro de bienestar y terapia corporal de <PERSON>.

## 🌟 Características

- **Landing page optimizada** para servicios de spa y bienestar
- **Diseño responsive** que funciona en todos los dispositivos
- **Sistema de reservas** con formulario interactivo
- **Galería de imágenes** organizada por tipo de tratamiento
- **Formulario de contacto** con envío real de emails
- **SEO optimizado** con meta tags y structured data
- **Performance optimizada** con lazy loading y code splitting

## 🚀 Tecnologías

- **React 19** con TypeScript
- **Vite** para build y desarrollo
- **Tailwind CSS** para estilos
- **EmailJS** para envío de emails
- **React Router** para navegación

## 📋 Prerequisitos

- Node.js 18 o superior
- npm o yarn

## 🛠️ Instalación y Desarrollo

1. **Clonar el repositorio**
   ```bash
   git clone [url-del-repositorio]
   cd bienestar-integral
   ```

2. **Instalar dependencias**
   ```bash
   npm install
   ```

3. **Configurar variables de entorno**

   Copia el archivo `.env.local` y configura las variables:
   ```bash
   # Opcional: Para funcionalidad de emails automáticos
   VITE_EMAILJS_SERVICE_ID=tu_service_id
   VITE_EMAILJS_TEMPLATE_ID=tu_template_id
   VITE_EMAILJS_PUBLIC_KEY=tu_public_key
   ```

4. **Ejecutar en desarrollo**
   ```bash
   npm run dev
   ```

5. **Abrir en el navegador**

   La aplicación estará disponible en `http://localhost:3000`

## 🔧 Scripts Disponibles

- `npm run dev` - Servidor de desarrollo
- `npm run build` - Build para producción
- `npm run preview` - Preview del build
- `npm run lint` - Verificar código con ESLint
- `npm run format` - Formatear código con Prettier
- `npm run type-check` - Verificar tipos de TypeScript

## 🚀 Deployment

### Netlify (Recomendado)

1. **Conectar repositorio**
   - Crear cuenta en [Netlify](https://netlify.com)
   - Conectar tu repositorio de GitHub

2. **Configuración automática**
   - Netlify detectará automáticamente la configuración desde `netlify.toml`
   - Build command: `npm run build`
   - Publish directory: `dist`

3. **Variables de entorno**
   - Configurar las variables de EmailJS en el panel de Netlify si deseas emails automáticos

### Vercel

1. **Conectar repositorio**
   - Crear cuenta en [Vercel](https://vercel.com)
   - Importar tu repositorio

2. **Configuración automática**
   - Vercel detectará automáticamente la configuración desde `vercel.json`

### Deployment manual

```bash
# Para Netlify
npm run deploy:netlify

# Para Vercel
npm run deploy:vercel
```

## 📧 Configuración de EmailJS (Opcional)

Para habilitar el envío automático de emails:

1. Crear cuenta en [EmailJS](https://www.emailjs.com/)
2. Configurar un servicio de email (Gmail, Outlook, etc.)
3. Crear un template de email
4. Obtener las claves y configurarlas en `.env.local`

Si no se configura EmailJS, el formulario usará `mailto:` como fallback.

## 📁 Estructura del Proyecto

```
spa-pascale/
├── components/          # Componentes React
│   ├── Hero.tsx        # Sección principal
│   ├── TreatmentsSection.tsx
│   └── ...
├── pages/              # Páginas de la aplicación
│   ├── HomePage.tsx
│   ├── ContactPage.tsx
│   └── ...
├── hooks/              # Custom hooks
├── services/           # Servicios (EmailJS, etc.)
├── public/             # Assets estáticos
│   ├── robots.txt
│   ├── sitemap.xml
│   └── ...
└── constants.ts        # Datos del spa
```

## 🎨 Personalización

### Colores del Brand
Los colores están definidos en `index.html` y `index.css`:
- Primary: `#0D9488` (Teal)
- Secondary: `#F0ABFC` (Fuchsia)
- Light: `#F0FDFA` (Teal light)

### Contenido
- Editar `constants.ts` para cambiar textos, servicios y testimonios
- Reemplazar imágenes en las URLs de Picsum por imágenes reales
- Actualizar información de contacto

## 📞 Soporte

Para soporte técnico o consultas sobre el desarrollo, contactar al desarrollador.

---

**SPA Pascale - Bienestar Integral** | Desarrollado con ❤️ para el bienestar
