'use client';

import React from 'react';
import { ABOUT_ME_TEXT } from '../constants';
import { useScrollAnimation } from '../hooks/useScrollAnimation';

export const AboutMeSection: React.FC = () => {
  // Trigger animation when 40% of the section is visible for a better user experience
  const [sectionRef, isVisible] = useScrollAnimation<HTMLElement>({
    threshold: 0.4,
  });

  return (
    <section
      id="sobre-mi"
      ref={sectionRef}
      className={`py-16 sm:py-24 bg-white transition-opacity duration-700 ease-out ${
        isVisible ? 'opacity-100' : 'opacity-0'
      }`}
    >
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="lg:grid lg:grid-cols-2 lg:gap-12 lg:items-center">
          {/* Text content with orchestrated animations */}
          <div>
            <h2 className="text-3xl font-serif font-bold text-brand-primary sm:text-4xl mb-6">
              {ABOUT_ME_TEXT.greeting.split(' ').map((word, index, arr) => (
                <span
                  key={index}
                  className="inline-block overflow-hidden pb-1 -mb-1"
                >
                  <span
                    className={`inline-block transition-transform duration-700 ease-out transform ${
                      isVisible ? 'translate-y-0' : 'translate-y-full'
                    }`}
                    style={{ transitionDelay: `${300 + index * 70}ms` }}
                  >
                    {word}
                    {index < arr.length - 1 && '\u00A0'}
                  </span>
                </span>
              ))}
            </h2>
            <div>
              <p className="text-lg text-brand-text-light mb-4 leading-relaxed">
                {ABOUT_ME_TEXT.introduction
                  .split(' ')
                  .map((word, index, arr) => (
                    <span
                      key={index}
                      className={`inline-block transition-all duration-500 ease-out transform-gpu ${
                        isVisible
                          ? 'opacity-100 translate-x-0'
                          : 'opacity-0 -translate-x-3'
                      }`}
                      style={{ transitionDelay: `${800 + index * 15}ms` }}
                    >
                      {word}
                      {index < arr.length - 1 && '\u00A0'}
                    </span>
                  ))}
              </p>
              <p className="text-lg text-brand-text-light leading-relaxed">
                {ABOUT_ME_TEXT.philosophy.split(' ').map((word, index, arr) => (
                  <span
                    key={index}
                    className={`inline-block transition-all duration-500 ease-out transform-gpu ${
                      isVisible
                        ? 'opacity-100 translate-x-0'
                        : 'opacity-0 -translate-x-3'
                    }`}
                    style={{
                      transitionDelay: `${800 + ABOUT_ME_TEXT.introduction.split(' ').length * 15 + index * 15}ms`,
                    }}
                  >
                    {word}
                    {index < arr.length - 1 && '\u00A0'}
                  </span>
                ))}
              </p>
            </div>
          </div>

          {/* Image with its own animation */}
          <div className="mt-10 lg:mt-0" aria-hidden="true">
            <div
              className={`transition-all duration-1000 ease-out ${isVisible ? 'opacity-100 scale-100' : 'opacity-0 scale-95'}`}
              style={{ transitionDelay: '100ms' }}
            >
              <img
                className="mx-auto rounded-lg shadow-2xl object-cover object-top w-full h-auto max-w-md lg:max-w-none lg:h-[450px]"
                src="https://i.ibb.co/wh8ymY05/enhanced-Captura2.png"
                alt="Retrato de Carolina Paz Gómez Garín, cosmetóloga y terapeuta corporal"
                loading="eager"
                fetchPriority="high"
                width="640"
                height="640"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
