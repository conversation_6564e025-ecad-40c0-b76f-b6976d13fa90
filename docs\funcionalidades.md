# DOCUMENTACIÓN DE FUNCIONALIDADES - SPA PASCALE

## Información del Documento
**Proyecto:** SPA Pascale - Bienestar Integral  
**Fecha de creación:** 25 de Julio de 2025  
**Versión:** 1.0  
**Responsable:** Agente de IA Senior  

---

## 1. FUNCIONALIDADES PRINCIPALES

### 1.1 Sistema de Navegación Inteligente
**Propósito:** Proporcionar navegación fluida y accesible entre todas las secciones del sitio  
**Ubicación:** `components/Navbar.tsx`  
**Uso:** Navegación automática disponible en todas las páginas  

**Características:**
- Navegación responsive con menú hamburguesa en móviles
- Smooth scrolling entre secciones
- Indicador visual de página activa
- Botón CTA destacado para "Agendar Cita"
- Accesibilidad completa con navegación por teclado

**Rutas implementadas:**
- `/` - Página principal
- `/sobre-carolina` - Información sobre la terapeuta
- `/tratamientos` - Catálogo de servicios
- `/galeria` - Galería visual
- `/contacto` - Formulario de contacto
- `/agenda` - Sistema de reservas

### 1.2 Hero Section con Carrusel Automático
**Propósito:** Crear primera impresión impactante y transmitir la filosofía del spa  
**Ubicación:** `components/Hero.tsx`  
**Uso:** Visualización automática en página principal  

**Características:**
- Carrusel de 15 imágenes inspiradoras de naturaleza
- Transición automática cada 7 segundos
- Preloading inteligente para rendimiento óptimo
- Overlay con mensaje principal y CTA
- Responsive design para todos los dispositivos

**Detalles técnicos:**
- Optimización de imágenes con lazy loading
- Accesibilidad con ARIA labels
- Smooth transitions con CSS
- Gestión de estado con React hooks

### 1.3 Sistema de Presentación de Servicios
**Propósito:** Mostrar detalladamente los 3 tratamientos especializados  
**Ubicación:** `components/TreatmentsSection.tsx`, `components/TreatmentCard.tsx`  
**Uso:** Información detallada de servicios disponibles  

**Servicios implementados:**

#### 1.3.1 Masaje de Relajación Profunda
- **Enfoque:** Mente-Cuerpo para calma total
- **Beneficios:** Reducción de estrés, mejora del sueño, claridad mental
- **Técnicas:** Maniobras suaves, equilibrio energético, inspiración en Reiki

#### 1.3.2 Masaje Descontracturante Terapéutico
- **Enfoque:** Alivio de tensión crónica y contracturas
- **Beneficios:** Alivio de dolores, aumento de flexibilidad, mejora circulatoria
- **Técnicas:** Presión focalizada, maniobras precisas, trabajo en capas profundas

#### 1.3.3 Drenaje Linfático Manual
- **Enfoque:** Estimulación del sistema linfático
- **Beneficios:** Reducción de hinchazón, desintoxicación, fortalecimiento inmune
- **Técnicas:** Masaje suave y rítmico, estimulación linfática

### 1.4 Sistema de Comunicación Dual
**Propósito:** Facilitar contacto directo y reservas de citas  
**Ubicación:** `services/emailService.ts`, `pages/ContactPage.tsx`, `pages/AgendaPage.tsx`  

#### 1.4.1 Formulario de Contacto General
**Campos disponibles:**
- Nombre completo
- Email de contacto
- Teléfono (opcional)
- Servicio de interés
- Mensaje personalizado

**Funcionalidades:**
- Envío automático vía EmailJS
- Fallback a mailto si EmailJS no está configurado
- Validación de campos obligatorios
- Confirmación de envío exitoso

#### 1.4.2 Sistema de Reservas
**Campos disponibles:**
- Información personal (nombre, email, teléfono)
- Selección de servicio
- Fecha preferida
- Hora preferida
- Notas adicionales

**Funcionalidades:**
- Integración con EmailJS para notificaciones automáticas
- Sistema de fallback seguro
- Validación de fechas y horarios
- Confirmación de reserva

### 1.5 Galería Visual Categorizada
**Propósito:** Mostrar ambiente y filosofía del spa a través de imágenes  
**Ubicación:** `pages/GalleryPage.tsx`  
**Uso:** Navegación visual por categorías de tratamiento  

**Organización:**
- **Relajación:** 6 imágenes de ambiente sereno y tranquilo
- **Descontracturante:** 6 imágenes de terapia y alivio muscular
- **Linfático:** 6 imágenes de desintoxicación y bienestar

**Características técnicas:**
- Lazy loading para optimización de carga
- Grid responsive adaptable
- Filtrado por categorías
- Optimización de imágenes automática

### 1.6 Sistema de Testimonios y Calificaciones
**Propósito:** Mostrar credibilidad y experiencias reales de clientes  
**Ubicación:** `components/RatingsTestimonialsSection.tsx`  
**Uso:** Visualización automática de testimonios verificados  

**Características:**
- 6 testimonios reales con calificaciones
- Sistema de estrellas (1-5 estrellas)
- Avatares generados dinámicamente
- Ubicaciones geográficas para credibilidad
- Rotación automática de testimonios destacados

### 1.7 Experiencia de Usuario Inmersiva
**Propósito:** Crear ambiente relajante y profesional  

#### 1.7.1 Pantalla de Introducción
**Ubicación:** `components/IntroScreen.tsx`  
**Funcionalidad:** Bienvenida animada al cargar el sitio  
**Características:**
- Animación de fade-in suave
- Logo y mensaje de bienvenida
- Transición automática a contenido principal
- Bloqueo de scroll durante introducción

#### 1.7.2 Partículas Flotantes
**Ubicación:** `components/FloatingParticles.tsx`  
**Funcionalidad:** Ambiente visual relajante  
**Características:**
- Partículas animadas en background
- Movimiento suave y orgánico
- No interfiere con contenido principal
- Optimizado para rendimiento

#### 1.7.3 Botón WhatsApp Flotante
**Ubicación:** `components/WhatsAppChatButton.tsx`  
**Funcionalidad:** Contacto directo instantáneo  
**Características:**
- Botón flotante siempre visible
- Integración directa con WhatsApp Business
- Mensaje predefinido personalizable
- Animación de pulso para llamar atención

## 2. FUNCIONALIDADES TÉCNICAS

### 2.1 Optimización de Rendimiento
**Hooks personalizados:**
- `useImagePreloader.ts`: Preloading inteligente de imágenes
- `useScrollAnimation.ts`: Animaciones basadas en scroll

**Optimizaciones de build:**
- Code splitting automático
- Minificación con Terser
- Eliminación de logs en producción
- Chunks manuales para librerías principales

### 2.2 Accesibilidad Web
- ARIA labels en todos los componentes interactivos
- Navegación por teclado completa
- Contraste de colores optimizado
- Textos alternativos en todas las imágenes
- Estructura semántica HTML5

### 2.3 SEO y Metadatos
- Meta tags optimizados
- Sitemap.xml generado
- Robots.txt configurado
- Structured data para servicios
- URLs amigables con HashRouter

## 3. CASOS DE USO RECOMENDADOS

### 3.1 Para Visitantes Nuevos
1. Visualización de Hero con mensaje principal
2. Lectura de información "Sobre Carolina"
3. Exploración de tratamientos disponibles
4. Revisión de testimonios para credibilidad
5. Contacto inicial vía formulario o WhatsApp

### 3.2 Para Clientes Interesados
1. Navegación directa a "Tratamientos"
2. Revisión detallada de servicios específicos
3. Exploración de galería por categoría de interés
4. Uso del sistema de reservas para agendar cita
5. Confirmación vía email automático

### 3.3 Para Clientes Recurrentes
1. Acceso directo a "Agendar Cita"
2. Contacto rápido vía WhatsApp
3. Exploración de nuevos servicios
4. Referencia de información de contacto

---

**Última actualización:** 25 de Julio de 2025  
**Próxima revisión:** 1 de Agosto de 2025
