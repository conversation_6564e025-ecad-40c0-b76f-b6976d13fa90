'use client';


import <PERSON>ript from 'next/script';
import Link from 'next/link';
import { TREATMENTS_DATA, TREATMENTS_INTRO_TEXT } from '../../constants';
import { TreatmentCard } from '../../components/TreatmentCard';
import { useScrollAnimation } from '../../hooks/useScrollAnimation';



export default function ServicesPage() {
  const [sectionRef, isVisible] = useScrollAnimation<HTMLDivElement>({
    threshold: 0.05,
  });

  const serviceSchemas = TREATMENTS_DATA.map(treatment => ({
    "@context": "https://schema.org",
    "@type": "Service",
    "name": treatment.title,
    "description": treatment.shortDescription,
    "url": `https://www.spapascale.com/tratamientos#${treatment.id}`, // Enlace a la sección del tratamiento
    "provider": {
      "@type": "Organization",
      "name": "SPA Pascale"
    },
    "serviceType": "Health and Beauty", // O más específico como "MassageTherapy"
    "areaServed": {
      "@type": "Place",
      "name": "Santiago, Chile" // O la ubicación específica de tu spa
    },
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "Catálogo de Servicios de SPA Pascale",
      "itemListElement": [
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": treatment.title
          }
        }
      ]
    }
  }));

  return (
    <>
      {serviceSchemas.map((schema, index) => (
        <Script
          key={index}
          id={`service-schema-${index}`}
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}
        />
      ))}
      <div
        ref={sectionRef}
        className={`py-16 sm:py-24 bg-brand-light transition-all duration-700 ease-out transform ${
          isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}
      >
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-serif font-bold text-brand-primary sm:text-5xl">
              Nuestros Tratamientos Terapéuticos
            </h1>
            <p className="mt-6 text-lg text-brand-text-light max-w-3xl mx-auto">
              {TREATMENTS_INTRO_TEXT} Descubre cómo cada terapia está diseñada
              para tu bienestar integral.
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            {TREATMENTS_DATA.map(treatment => (
              <TreatmentCard key={treatment.id} treatment={treatment} />
            ))}
          </div>
          <div className="text-center mt-16">
            <h2 className="text-2xl font-serif font-semibold text-brand-primary mb-4">
              ¿Lista para comenzar tu viaje hacia el bienestar?
            </h2>
            <p className="text-brand-text-light mb-6 max-w-xl mx-auto">
              Si tienes alguna pregunta o deseas agendar tu cita, no dudes en
              contactarme. Estoy aquí para ayudarte a encontrar el tratamiento
              perfecto para ti.
            </p>
            <Link
              href="/contacto"
              className="px-8 py-3 bg-brand-primary hover:bg-brand-dark text-white font-semibold rounded-lg shadow-lg transition-transform duration-300 ease-in-out transform hover:scale-105"
            >
              Contactar y Agendar
            </Link>
          </div>
        </div>
      </div>
    </>
  );
}
