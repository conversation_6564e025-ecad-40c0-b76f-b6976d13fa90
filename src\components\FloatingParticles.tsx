'use client';

import React, { useEffect, useRef } from 'react';

const PARTICLE_CONFIG = {
  count: 40, // A subtle number of particles
  minSpeed: 0.05,
  maxSpeed: 0.4,
  minRadius: 1,
  maxRadius: 3,
};

interface Particle {
  x: number;
  y: number;
  radius: number;
  speed: number;
  opacity: number;
}

export const FloatingParticles: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const particlesRef = useRef<Particle[]>([]);
  const animationFrameId = useRef<number | undefined>(undefined);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const createParticle = (yPos?: number): Particle => {
      const finalY = yPos === undefined ? Math.random() * canvas.height : yPos;
      return {
        x: Math.random() * canvas.width,
        y: finalY,
        radius:
          Math.random() *
            (PARTICLE_CONFIG.maxRadius - PARTICLE_CONFIG.minRadius) +
          PARTICLE_CONFIG.minRadius,
        speed:
          Math.random() *
            (PARTICLE_CONFIG.maxSpeed - PARTICLE_CONFIG.minSpeed) +
          PARTICLE_CONFIG.minSpeed,
        opacity: Math.random() * 0.5 + 0.1, // Keep it subtle, slightly more visible
      };
    };

    const setupParticles = () => {
      const particles: Particle[] = [];
      for (let i = 0; i < PARTICLE_CONFIG.count; i++) {
        // Distribute particles across the full height initially
        particles.push(createParticle(Math.random() * canvas.height));
      }
      particlesRef.current = particles;
    };

    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
      setupParticles();
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    const animate = () => {
      if (!canvas || !ctx) return;
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      particlesRef.current.forEach((particle, index) => {
        particle.y -= particle.speed;

        // Draw particle
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.radius, 0, Math.PI * 2);
        // Changed to translucent white for better visibility over various background colors
        ctx.fillStyle = `rgba(255, 255, 255, ${particle.opacity})`;
        ctx.fill();

        // Reset particle if it's off-screen (moved above the top)
        if (particle.y < -particle.radius) {
          particlesRef.current[index] = createParticle(
            canvas.height + Math.random() * 50
          );
        }
      });

      animationFrameId.current = requestAnimationFrame(animate);
    };

    animate();

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      if (animationFrameId.current) {
        cancelAnimationFrame(animationFrameId.current);
      }
    };
  }, []);

  return (
    <canvas
      ref={canvasRef}
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        // Increased z-index to be above page content but below UI elements like the navbar.
        zIndex: 5,
        pointerEvents: 'none',
      }}
      aria-hidden="true"
    />
  );
};