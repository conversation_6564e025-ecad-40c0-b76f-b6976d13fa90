# DOCUMENTACIÓN DE CONFIGURACIÓN DEL ENTORNO - SPA PASCALE

## Información del Documento
**Proyecto:** SPA Pascale - Bienestar Integral  
**Fecha de creación:** 25 de Julio de 2025  
**Versión:** 1.0  
**Responsable:** Agente de IA Senior  

---

## 1. CONFIGURACIÓN DEL ENTORNO DE DESARROLLO

### 1.1 Requisitos del Sistema
- **Node.js:** Versión 18.0.0 o superior
- **npm:** Versión 8.0.0 o superior (incluido con Node.js)
- **Git:** Para control de versiones
- **Editor recomendado:** Visual Studio Code con extensiones TypeScript y React

### 1.2 Variables de Entorno

#### Archivo `.env.local` (Desarrollo)
```bash
# EmailJS Configuration (Opcional pero recomendado)
VITE_EMAILJS_SERVICE_ID=your_service_id_here
VITE_EMAILJS_TEMPLATE_ID=your_template_id_here
VITE_EMAILJS_PUBLIC_KEY=your_public_key_here

# API Keys para funcionalidades futuras
GEMINI_API_KEY=your_gemini_api_key_here
```

#### Configuración de EmailJS
1. **Crear cuenta en EmailJS:** https://www.emailjs.com/
2. **Configurar servicio de email:**
   - Gmail, Outlook, o proveedor preferido
   - Obtener Service ID
3. **Crear template de email:**
   - Template para contacto general
   - Template para reservas de citas
   - Obtener Template ID
4. **Obtener Public Key:**
   - Configurar en dashboard de EmailJS
   - Copiar Public Key

#### Validación de Configuración
```typescript
// Función implementada en emailService.ts
export const isEmailJSConfigured = (): boolean => {
  return !!(EMAILJS_SERVICE_ID && EMAILJS_TEMPLATE_ID && EMAILJS_PUBLIC_KEY &&
    EMAILJS_SERVICE_ID !== 'your_service_id' &&
    EMAILJS_TEMPLATE_ID !== 'your_template_id' &&
    EMAILJS_PUBLIC_KEY !== 'your_public_key');
};
```

### 1.3 Configuración de TypeScript

#### tsconfig.json
```json
{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "isolatedModules": true,
    "moduleDetection": "force",
    "noEmit": true,
    "jsx": "react-jsx",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedIndexedAccess": true
  },
  "include": ["src"]
}
```

**Características habilitadas:**
- **Strict mode:** Máxima verificación de tipos
- **No unused locals/parameters:** Limpieza de código
- **JSX:** react-jsx para React 17+ transform
- **Module resolution:** bundler para Vite

### 1.4 Configuración de Vite

#### vite.config.ts
```typescript
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, '.', '');
  return {
    define: {
      'process.env.API_KEY': JSON.stringify(env.GEMINI_API_KEY),
      'process.env.GEMINI_API_KEY': JSON.stringify(env.GEMINI_API_KEY),
    },
    resolve: {
      alias: {
        '@': path.resolve(__dirname, '.'),
      },
    },
    build: {
      target: 'es2015',
      minify: 'terser',
      cssMinify: true,
      rollupOptions: {
        output: {
          manualChunks: {
            react: ['react', 'react-dom'],
            router: ['react-router-dom'],
          },
        },
      },
      terserOptions: {
        compress: {
          drop_console: true,
          drop_debugger: true,
        },
      },
    },
    server: {
      port: 3000,
      open: true,
    },
    preview: {
      port: 4173,
    },
  };
});
```

**Optimizaciones configuradas:**
- **Code splitting:** React y Router en chunks separados
- **Minificación:** Terser con eliminación de logs
- **Alias:** @ para imports relativos
- **Puertos:** 3000 para dev, 4173 para preview

## 2. CONFIGURACIÓN DE LINTING Y FORMATEO

### 2.1 ESLint Configuration

#### eslint.config.js
```javascript
import js from '@eslint/js'
import globals from 'globals'
import reactRefresh from 'eslint-plugin-react-refresh'
import tseslint from 'typescript-eslint'

export default tseslint.config(
  { ignores: ['dist'] },
  {
    extends: [js.configs.recommended, ...tseslint.configs.recommended],
    files: ['**/*.{ts,tsx}'],
    languageOptions: {
      ecmaVersion: 2020,
      globals: globals.browser,
    },
    plugins: {
      'react-refresh': reactRefresh,
    },
    rules: {
      'react-refresh/only-export-components': [
        'warn',
        { allowConstantExport: true },
      ],
    },
  },
)
```

### 2.2 Prettier Configuration
```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false
}
```

## 3. CONFIGURACIÓN DE DEPLOYMENT

### 3.1 Netlify Configuration

#### netlify.toml
```toml
[build]
  publish = "dist"
  command = "npm run build"

[build.environment]
  NODE_VERSION = "18"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
```

#### Variables de entorno en Netlify
1. **Acceder a Site Settings → Environment Variables**
2. **Agregar variables:**
   - `VITE_EMAILJS_SERVICE_ID`
   - `VITE_EMAILJS_TEMPLATE_ID`
   - `VITE_EMAILJS_PUBLIC_KEY`

### 3.2 Vercel Configuration

#### vercel.json
```json
{
  "buildCommand": "npm run build",
  "outputDirectory": "dist",
  "devCommand": "npm run dev",
  "installCommand": "npm install",
  "framework": "vite",
  "rewrites": [
    {
      "source": "/(.*)",
      "destination": "/index.html"
    }
  ],
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Frame-Options",
          "value": "DENY"
        },
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        },
        {
          "key": "X-XSS-Protection",
          "value": "1; mode=block"
        },
        {
          "key": "Referrer-Policy",
          "value": "strict-origin-when-cross-origin"
        }
      ]
    }
  ]
}
```

## 4. CONFIGURACIÓN DE SEO

### 4.1 Meta Tags (index.html)
```html
<meta charset="UTF-8" />
<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
<meta name="viewport" content="width=device-width, initial-scale=1.0" />
<meta name="description" content="SPA Pascale - Bienestar Integral. Masajes terapéuticos, relajación profunda y drenaje linfático con Carolina Paz Gómez Garín, cosmetóloga titulada." />
<meta name="keywords" content="spa, masajes, bienestar, relajación, drenaje linfático, terapia corporal, Carolina Paz" />
<meta name="author" content="Carolina Paz Gómez Garín" />
<title>SPA Pascale - Bienestar Integral</title>
```

### 4.2 Sitemap.xml
```xml
<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>https://spapascale.com/</loc>
    <lastmod>2025-07-25</lastmod>
    <changefreq>weekly</changefreq>
    <priority>1.0</priority>
  </url>
  <url>
    <loc>https://spapascale.com/#/sobre-carolina</loc>
    <lastmod>2025-07-25</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.8</priority>
  </url>
  <url>
    <loc>https://spapascale.com/#/tratamientos</loc>
    <lastmod>2025-07-25</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.9</priority>
  </url>
</urlset>
```

### 4.3 Robots.txt
```
User-agent: *
Allow: /

Sitemap: https://spapascale.com/sitemap.xml
```

## 5. CONFIGURACIÓN DE PWA

### 5.1 Manifest.json
```json
{
  "name": "SPA Pascale - Bienestar Integral",
  "short_name": "SPA Pascale",
  "description": "Centro de bienestar y terapia corporal",
  "start_url": "/",
  "display": "standalone",
  "background_color": "#F0FDFA",
  "theme_color": "#0D9488",
  "icons": [
    {
      "src": "/favicon.svg",
      "sizes": "any",
      "type": "image/svg+xml"
    }
  ]
}
```

## 6. CONFIGURACIÓN DE SEGURIDAD

### 6.1 Headers de Seguridad
- **X-Frame-Options:** DENY (previene clickjacking)
- **X-XSS-Protection:** 1; mode=block (protección XSS)
- **X-Content-Type-Options:** nosniff (previene MIME sniffing)
- **Referrer-Policy:** strict-origin-when-cross-origin

### 6.2 HTTPS Configuration
- **Netlify:** HTTPS forzado automáticamente
- **Vercel:** HTTPS forzado automáticamente
- **Custom domain:** Certificado SSL automático

## 7. COMANDOS DE CONFIGURACIÓN

### 7.1 Setup Inicial
```bash
# Clonar repositorio
git clone [repository-url]
cd spa-pascale

# Instalar dependencias
npm install

# Crear archivo de entorno
cp .env.example .env.local
# Editar .env.local con valores reales

# Verificar configuración
npm run type-check
npm run lint
```

### 7.2 Verificación de Configuración
```bash
# Verificar tipos TypeScript
npm run type-check

# Verificar linting
npm run lint

# Verificar formateo
npm run format:check

# Verificar build
npm run build

# Verificar preview
npm run preview
```

---

**Última actualización:** 25 de Julio de 2025  
**Próxima revisión:** 1 de Agosto de 2025
