'use client';

import React, { useState, useEffect, useRef } from 'react';
import { SiteLogo } from './icons/SiteLogo';

interface IntroScreenProps {
  onFinished: () => void;
}

export const IntroScreen: React.FC<IntroScreenProps> = ({ onFinished }) => {
  const [step, setStep] = useState<
    'fadeInScreen' | 'showContent' | 'fadeOutScreen'
  >('fadeInScreen');
  const [progress, setProgress] = useState(0); // For progress bar

  const contentTimerRef = useRef<number | undefined>(undefined);
  const fadeOutTimerRef = useRef<number | undefined>(undefined);
  const finishedTimerRef = useRef<number | undefined>(undefined);
  const progressAnimationRef = useRef<number | undefined>(undefined);

  // Effect for step transitions
  useEffect(() => {
    if (step === 'fadeInScreen') {
      if (contentTimerRef.current !== undefined)
        window.clearTimeout(contentTimerRef.current);
      if (fadeOutTimerRef.current !== undefined)
        window.clearTimeout(fadeOutTimerRef.current);
      if (finishedTimerRef.current !== undefined)
        window.clearTimeout(finishedTimerRef.current);

      contentTimerRef.current = window.setTimeout(() => {
        setStep('showContent');
      }, 500); // Increased from 300ms

      fadeOutTimerRef.current = window.setTimeout(() => {
        setStep('fadeOutScreen');
      }, 4500); // Increased from 2800ms
    } else if (step === 'fadeOutScreen') {
      if (contentTimerRef.current !== undefined)
        window.clearTimeout(contentTimerRef.current);

      finishedTimerRef.current = window.setTimeout(onFinished, 1000); // Increased from 700ms
    }
  }, [step, onFinished]);

  // Effect for progress bar animation
  useEffect(() => {
    if (step === 'showContent') {
      let startTime: number | null = null;
      const animationDuration = 4000; // Increased from 2500ms (4500ms fadeOut - 500ms content)

      const animate = (timestamp: number) => {
        if (!startTime) {
          startTime = timestamp;
        }
        const elapsedTime = timestamp - startTime;
        const calculatedProgress = Math.min(
          (elapsedTime / animationDuration) * 100,
          100
        );
        setProgress(calculatedProgress);

        if (elapsedTime < animationDuration) {
          progressAnimationRef.current = requestAnimationFrame(animate);
        } else {
          setProgress(100);
        }
      };

      setProgress(0);
      progressAnimationRef.current = requestAnimationFrame(animate);

      return () => {
        if (progressAnimationRef.current !== undefined) {
          cancelAnimationFrame(progressAnimationRef.current);
        }
      };
    } else {
      setProgress(0);
      if (progressAnimationRef.current !== undefined) {
        cancelAnimationFrame(progressAnimationRef.current);
      }
    }
  }, [step]);

  // Combined cleanup effect for all timers on component unmount
  useEffect(() => {
    return () => {
      if (contentTimerRef.current !== undefined)
        window.clearTimeout(contentTimerRef.current);
      if (fadeOutTimerRef.current !== undefined)
        window.clearTimeout(fadeOutTimerRef.current);
      if (finishedTimerRef.current !== undefined)
        window.clearTimeout(finishedTimerRef.current);
      if (progressAnimationRef.current !== undefined) {
        cancelAnimationFrame(progressAnimationRef.current);
      }
    };
  }, []);

  return (
    <div
      className={`fixed inset-0 z-[100] transition-opacity duration-1000 ease-in-out bg-gradient-to-br from-brand-light via-teal-100 to-brand-primary/30
        ${step === 'fadeInScreen' || step === 'showContent' ? 'opacity-100' : 'opacity-0'}
      `}
      aria-label="Pantalla de bienvenida"
      role="dialog"
      aria-modal="true"
    >
      {/* Content Wrapper */}
      <div className="absolute inset-0 flex flex-col items-center justify-center">
        <div
          className={`flex flex-col items-center transition-all duration-1000 ease-in-out transform ${
            step === 'showContent'
              ? 'opacity-100 scale-100'
              : 'opacity-0 scale-90'
          }`}
        >
          <SiteLogo className="h-28 w-auto md:h-36 text-brand-primary animate-subtle-pulse" />
          <p
            className={`mt-6 text-xl md:text-2xl font-serif text-brand-dark transition-all duration-700 ease-in-out ${
              step === 'showContent'
                ? 'opacity-100 animate-fade-in-up'
                : 'opacity-0'
            }`}
            style={{ animationDelay: step === 'showContent' ? '0.3s' : '0s' }}
          >
            Cargando Bienestar...
          </p>

          <div
            className={`w-56 h-2.5 bg-white/70 rounded-full overflow-hidden mt-6 transition-opacity duration-300 ease-in-out ${
              step === 'showContent'
                ? 'opacity-100'
                : 'opacity-0 pointer-events-none'
            }`}
            role="progressbar"
            aria-valuenow={Math.round(progress)}
            aria-valuemin={0}
            aria-valuemax={100}
            aria-label="Progreso de carga"
          >
            <div
              className="h-full bg-brand-primary rounded-full"
              style={{ width: `${progress}%` }}
            ></div>
          </div>
          <p
            className={`mt-2 text-sm text-brand-dark transition-opacity duration-300 ease-in-out ${
              step === 'showContent' ? 'opacity-100' : 'opacity-0'
            }`}
            aria-hidden="true" // Value conveyed by progressbar's aria-valuenow
          >
            {Math.round(progress)}%
          </p>
        </div>
      </div>
    </div>
  );
};