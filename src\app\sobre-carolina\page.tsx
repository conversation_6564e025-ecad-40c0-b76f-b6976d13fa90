'use client';


import Script from 'next/script';
import Link from 'next/link';
import Image from 'next/image';
import { ABOUT_ME_TEXT } from '../../constants';
import { useScrollAnimation } from '../../hooks/useScrollAnimation';



export default function AboutPage() {
  const [sectionRef, isVisible] = useScrollAnimation<HTMLDivElement>({
    threshold: 0.05,
  });

  const personSchema = {
    "@context": "https://schema.org",
    "@type": "Person",
    "name": "<PERSON> Paz <PERSON>ín",
    "url": "https://www.spapascale.com/sobre-carolina", // Reemplazar con tu URL real
    "image": "https://i.ibb.co/wh8ymY05/enhanced-Captura2.png", // URL de la imagen de Carolina
    "alumniOf": "[Nombre de tu institución de cosmetología]", // Opcional: si quieres añadir tu alma mater
    "jobTitle": "Cosmetóloga y Terapeuta Corporal",
    "worksFor": {
      "@type": "Organization",
      "name": "SPA Pascale"
    },
    "sameAs": [
      "https://www.linkedin.com/in/carolina-paz-gomez-garin", // Reemplazar con tu LinkedIn
      // Añadir otras redes sociales personales o profesionales
    ]
  };

  return (
    <>
      <Script
        id="person-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(personSchema) }}
      />
      <div
        ref={sectionRef}
        className={`py-16 sm:py-24 bg-white transition-all duration-700 ease-out transform ${
          isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}
      >
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <Image
              className="mx-auto rounded-full shadow-2xl object-cover w-48 h-48 mb-8 border-2 border-brand-primary/50"
              src="https://i.ibb.co/wh8ymY05/enhanced-Captura2.png"
              alt="Retrato de Carolina Paz Gómez Garín"
              width={192}
              height={192}
              priority
            />
            <h1 className="text-4xl font-serif font-bold text-brand-primary sm:text-5xl">
              Sobre Carolina Paz Gómez Garín
            </h1>
          </div>

          <div className="prose prose-lg lg:prose-xl max-w-none text-brand-text-light leading-relaxed">
            <p className="text-xl font-semibold text-brand-primary">
              {ABOUT_ME_TEXT.greeting}
            </p>
            <p>{ABOUT_ME_TEXT.introduction}</p>
            <p>{ABOUT_ME_TEXT.philosophy}</p>

            <h2 className="font-serif text-2xl text-brand-primary mt-12 mb-4">
              Mi Compromiso Contigo
            </h2>
            <p>
              Mi enfoque va más allá de la técnica; se centra en la conexión
              humana y en crear un ambiente de confianza y confort. Cada sesión es
              una oportunidad para escucharte, entender tus necesidades y trabajar
              juntas hacia tus objetivos de bienestar. Utilizo productos de la más
              alta calidad, cuidadosamente seleccionados por sus propiedades
              beneficiosas y su respeto por la piel y el medio ambiente.
            </p>
            <p>
              Te invito a explorar los tratamientos y a dar el primer paso hacia
              una versión más equilibrada y radiante de ti misma. Estoy aquí para
              acompañarte en este hermoso viaje de autocuidado y transformación.
            </p>
          </div>

          <div className="text-center mt-16">
            <Link
              href="/contacto"
              className="px-8 py-3 bg-brand-primary hover:bg-brand-dark text-white font-semibold rounded-lg shadow-lg transition-transform duration-300 ease-in-out transform hover:scale-105"
            >
              Agenda tu Cita o Consulta
            </Link>
          </div>
        </div>
      </div>
    </>
  );
}
