'use client';

import React from 'react';
import { StarIcon } from './icons/StarIcon';
import { StarHalfIcon } from './icons/StarHalfIcon';

interface StarRatingDisplayProps {
  rating: number;
  totalStars?: number;
  starColor?: string;
  emptyStarColor?: string;
}

export const StarRatingDisplay: React.FC<StarRatingDisplayProps> = ({
  rating,
  totalStars = 5,
  starColor = 'text-yellow-400', // Default filled star color
  emptyStarColor = 'text-slate-300', // Default empty star color
}) => {
  const stars = [];
  for (let i = 1; i <= totalStars; i++) {
    if (rating >= i) {
      stars.push(
        <StarIcon key={`star-${i}`} className={`${starColor} w-5 h-5`} />
      );
    } else if (rating >= i - 0.5) {
      stars.push(
        <StarHalfIcon key={`star-${i}`} className={`${starColor} w-5 h-5`} />
      );
    } else {
      stars.push(
        <StarIcon key={`star-${i}`} className={`${emptyStarColor} w-5 h-5`} />
      );
    }
  }

  return <div className="flex items-center">{stars}</div>;
};
