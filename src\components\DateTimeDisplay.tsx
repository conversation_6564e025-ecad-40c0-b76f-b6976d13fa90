'use client';

import React, { useState, useEffect } from 'react';

export const DateTimeDisplay: React.FC = () => {
  const [dateTime, setDateTime] = useState(new Date());

  useEffect(() => {
    const timerId = setInterval(() => {
      setDateTime(new Date());
    }, 1000); // Update every second

    return () => clearInterval(timerId); // Cleanup interval on component unmount
  }, []);

  const formatDate = (date: Date): string => {
    return new Intl.DateTimeFormat('es-CL', {
      weekday: 'long',
      day: 'numeric',
      month: 'long',
      timeZone: 'America/Santiago',
    }).format(date);
  };

  const formatTime = (date: Date): string => {
    return new Intl.DateTimeFormat('es-CL', {
      hour: '2-digit',
      minute: '2-digit',
      // second: '2-digit', // Seconds removed
      hour12: false,
      timeZone: 'America/Santiago',
    }).format(date);
  };

  const capitalizeFirstLetter = (string: string) => {
    return string.charAt(0).toUpperCase() + string.slice(1);
  };

  // Capitalize weekday and month for a consistent display
  const displayDate = capitalizeFirstLetter(formatDate(dateTime));
  const displayTime = formatTime(dateTime);

  return (
    // Hidden on screens smaller than md (768px) to avoid clutter
    // Uses flex-col to stack date and time, items-center to align them center
    <div
      className="text-xs text-brand-text-light hidden md:flex md:flex-col items-center"
      aria-live="polite"
      aria-atomic="true"
    >
      <span aria-label="Fecha actual en Chile">{displayDate}</span>
      <span aria-label="Hora actual en Chile">{displayTime}</span>
    </div>
  );
};
