import React from 'react';
import Script from 'next/script';
import { Metadata } from 'next';
import { Playfair_Display, Lato } from 'next/font/google'; // Importar fuentes de Google
import '../../styles/globals.css';
import { ClientLayout } from '../components/ClientLayout';

export const metadata: Metadata = {
  title: 'SPA Pascale - Bienestar Integral | Masajes Terapéuticos en Valparaíso',
  description: 'Centro de bienestar integral especializado en masajes terapéuticos, relajación profunda y drenaje linfático. Carolina Paz <PERSON>, cosmetóloga titulada.',
  keywords: 'spa, masajes, bienestar, relajación, drenaje linf<PERSON>, Valpara<PERSON>o, Carolina <PERSON>',
  authors: [{ name: 'SPA Pascale' }],
  openGraph: {
    title: 'SPA Pascale - Bienestar Integral',
    description: 'Centro de bienestar integral especializado en masajes terapéuticos y relajación profunda.',
    url: 'https://pascale-spa.web.app',
    siteName: 'SPA Pascale',
    locale: 'es_CL',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'SPA Pascale - Bienestar Integral',
    description: 'Centro de bienestar integral especializado en masajes terapéuticos y relajación profunda.',
  },
  robots: {
    index: true,
    follow: true,
  },
};

// Configuración de las fuentes
const playfair = Playfair_Display({
  subsets: ['latin'],
  variable: '--font-playfair',
  display: 'swap',
});

const lato = Lato({
  weight: ['300', '400', '700'], // Pesos de fuente que necesitas
  subsets: ['latin'],
  variable: '--font-lato',
  display: 'swap',
});

export default function RootLayout({ children }: { children: React.ReactNode }) {

  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "SPA Pascale",
    "url": "https://www.spapascale.com", // Reemplazar con tu URL real
    "logo": "https://www.spapascale.com/logo.png", // Reemplazar con la URL de tu logo
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "+56977509997", // Reemplazar con tu número de teléfono real
      "contactType": "customer service"
    },
    "sameAs": [
      "https://web.facebook.com/Spa.Pascale/", // Reemplazar con tus redes sociales
      "https://www.instagram.com/pascalespa2025/",
      "https://x.com/spascale2025/",
      // Añadir más redes sociales si aplica
    ]
  };

  const websiteSchema = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "url": "https://www.spapascale.com", // Reemplazar con tu URL real
    "name": "SPA Pascale",
    "potentialAction": {
      "@type": "SearchAction",
      "target": "https://www.spapascale.com/search?q={search_term_string}", // Reemplazar con tu URL de búsqueda si tienes una
      "query-input": "required name=search_term_string"
    }
  };

  return (
    <html lang="es">
      <body className={`${playfair.variable} ${lato.variable}`}>
        <Script
          id="organization-schema"
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(organizationSchema) }}
        />
        <Script
          id="website-schema"
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(websiteSchema) }}
        />
        <ClientLayout>
          {children}
        </ClientLayout>
      </body>
    </html>
  );
}
