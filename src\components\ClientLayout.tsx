'use client';

import React, { useState, useEffect } from 'react';
import { Navbar } from './Navbar';
import { Footer } from './Footer';
import { WhatsAppChatButton } from './WhatsAppChatButton';
import { IntroScreen } from './IntroScreen';
import { FloatingParticles } from './FloatingParticles';
import { WHATSAPP_PHONE_NUMBER, WHATSAPP_GREETING_MESSAGE } from '../constants';

interface ClientLayoutProps {
  children: React.ReactNode;
}

export const ClientLayout: React.FC<ClientLayoutProps> = ({ children }) => {
  const [isIntroVisible, setIsIntroVisible] = useState(true);

  const handleIntroFinished = () => {
    setIsIntroVisible(false);
  };

  useEffect(() => {
    if (isIntroVisible) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }
    return () => {
      document.body.style.overflow = 'auto';
    };
  }, [isIntroVisible]);

  return (
    <>
      {isIntroVisible ? (
        <IntroScreen onFinished={handleIntroFinished} />
      ) : (
        <div className="flex flex-col min-h-screen">
          <FloatingParticles />
          <Navbar />
          <main className="flex-grow relative z-[1]">
            {children}
          </main>
          <Footer />
          <WhatsAppChatButton
            phoneNumber={WHATSAPP_PHONE_NUMBER}
            message={WHATSAPP_GREETING_MESSAGE}
          />
        </div>
      )}
    </>
  );
};
