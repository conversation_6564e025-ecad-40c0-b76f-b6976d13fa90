'use client';

import React from 'react';
import Link from 'next/link';
import { Treatment } from '../types';
import { LeafIcon } from './icons/LeafIcon';

interface TreatmentCardProps {
  treatment: Treatment;
}

export const TreatmentCard: React.FC<TreatmentCardProps> = ({ treatment }) => {
  return (
    <div className="group relative bg-white rounded-xl shadow-xl overflow-hidden flex flex-col transition-all duration-300 hover:shadow-2xl hover:scale-[1.03]">
      {/* Subtle Inner Glow / Halo Effect */}
      <div
        className="absolute inset-0 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"
        style={{
          background:
            'radial-gradient(circle at center, rgba(13, 148, 136, 0) 0%, rgba(13, 148, 136, 0.05) 70%, rgba(13, 148, 136, 0.15) 100%)',
        }}
        aria-hidden="true"
      />

      {/* Card Content (needs to be relative to sit on top of the effect) */}
      <div className="relative p-6 flex-grow">
        <div className="flex items-center mb-3">
          {React.isValidElement(treatment.icon) && (
            <span className="text-brand-primary mr-3">
              {React.cloneElement(treatment.icon, { className: 'w-7 h-7' })}
            </span>
          )}
          <h3 className="text-xl font-serif font-semibold text-brand-primary">
            {treatment.title}
          </h3>
        </div>
        <p className="text-sm text-brand-text-light mb-4 leading-relaxed line-clamp-4 hover:line-clamp-none transition-all duration-300">
          {' '}
          {/* Added line-clamp for brevity, expands on hover */}
          {treatment.longDescription}
        </p>

        <div className="mt-auto">
          <h4 className="text-md font-semibold text-brand-text mb-2">
            Beneficios clave:
          </h4>
          <ul className="space-y-1">
            {treatment.benefits.map((benefit, index) => (
              <li
                key={index}
                className="flex items-start text-sm text-brand-text-light"
              >
                <span className="text-brand-primary mr-2 mt-1 flex-shrink-0">
                  <LeafIcon width={16} height={16} />
                </span>
                {benefit}
              </li>
            ))}
          </ul>
        </div>
      </div>
      <div className="relative p-6 bg-slate-50">
        <Link
          href={
            {
              pathname: '/agenda',
              query: { service: treatment.title }, // Pass selected service as query parameter
            }
          }
          className="block w-full text-center px-4 py-2 text-sm font-medium text-white bg-brand-primary hover:bg-brand-dark rounded-md shadow-md hover:shadow-lg transition-all duration-300 ease-in-out"
        >
          Saber Más y Agendar
        </Link>
      </div>
    </div>
  );
};
