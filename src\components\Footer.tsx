'use client';

import React from 'react';
import { FacebookIcon } from './icons/FacebookIcon';
import { InstagramIcon } from './icons/InstagramIcon';
import { XIcon } from './icons/XIcon';
import { RedditIcon } from './icons/RedditIcon';
import { LinkedInIcon } from './icons/LinkedInIcon';

export const Footer: React.FC = () => {
  const socialLinks = [
    {
      name: 'Facebook',
      href: 'https://web.facebook.com/Spa.Pascale/',
      icon: <FacebookIcon className="h-6 w-6" />,
      ariaLabel: 'Visita nuestra página de Facebook',
    },
    {
      name: 'Instagram',
      href: 'https://www.instagram.com/pascalespa2025/',
      icon: <InstagramIcon className="h-6 w-6" />,
      ariaLabel: 'Síguenos en Instagram',
    },
    {
      name: 'X',
      href: 'https://x.com/spascale2025/',
      icon: <XIcon className="h-6 w-6" />,
      ariaLabel: 'Síguenos en X (antes Twitter)',
    },
    {
      name: 'Reddit',
      href: '#',
      icon: <RedditIcon className="h-6 w-6" />,
      ariaLabel: 'Únete a nuestra comunidad en Reddit',
    },
    {
      name: 'LinkedIn',
      href: '#',
      icon: <LinkedInIcon className="h-6 w-6" />,
      ariaLabel: 'Conecta con nosotros en LinkedIn',
    },
  ];

  return (
    <footer className="bg-slate-800 text-slate-300 py-8 relative z-[1]">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <p className="text-sm">
          &copy; {new Date().getFullYear()} SPA Pascale. Todos los derechos
          reservados.
        </p>
        <p className="text-xs mt-2">
          Diseñado por{' '}
          <span role="img" aria-label="love">
            Daniel Misle
          </span>{' '}
          para el Bienestar.
        </p>

        <div className="mt-6 flex justify-center space-x-5">
          {socialLinks.map(social => (
            <a
              key={social.name}
              href={social.href}
              className="text-slate-400 hover:text-white transform hover:scale-125 transition-transform duration-300 ease-in-out"
              aria-label={social.ariaLabel}
              target="_blank"
              rel="noopener noreferrer"
            >
              {social.icon}
            </a>
          ))}
        </div>
      </div>
    </footer>
  );
};