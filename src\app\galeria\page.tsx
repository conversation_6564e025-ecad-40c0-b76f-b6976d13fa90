'use client';


import Script from 'next/script';

import React from 'react';
import Image from 'next/image';
import { GALLERY_IMAGES_DATA, TREATMENTS_DATA } from '../../constants';
import { GalleryImage, Treatment } from '../../types';
import { useScrollAnimation } from '../../hooks/useScrollAnimation';
import { CloseIcon } from '../../components/icons/CloseIcon';



// --- Sub-componente: Tarjeta de Imagen de la Galería ---
const GalleryImageCard: React.FC<{
  image: GalleryImage;
  onClick: () => void;
}> = ({ image, onClick }) => {
  const [isLoaded, setIsLoaded] = useState(false);

  return (
    <div
      onClick={onClick}
      className="group aspect-w-1 aspect-h-1 bg-slate-200 rounded-lg shadow-lg overflow-hidden transition-all duration-300 hover:shadow-xl hover:scale-105 cursor-pointer"
      role="button"
      tabIndex={0}
      onKeyDown={e => (e.key === 'Enter' || e.key === ' ') && onClick()}
      aria-label={`Ver imagen ampliada: ${image.alt}`}
    >
      <div className="relative w-full h-full">
        {/* Placeholder con animación de pulso mientras carga la imagen */}
        {!isLoaded && (
          <div className="absolute inset-0 bg-slate-200 animate-pulse" />
        )}

        <Image
          src={image.src}
          alt={image.alt}
          className={`w-full h-full object-cover transition-opacity duration-700 ${isLoaded ? 'opacity-100' : 'opacity-0'}`}
          loading="lazy"
          decoding="async"
          fill // Use fill to make the image cover the parent div
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          onLoad={() => setIsLoaded(true)}
        />

        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-opacity duration-300 flex items-center justify-center p-4">
          <p className="text-white text-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300 text-center pointer-events-none">
            {image.alt}
          </p>
        </div>
      </div>
    </div>
  );
};

// --- Sub-componente: Lightbox para visualización de imágenes ---
const Lightbox: React.FC<{
  images: GalleryImage[];
  startIndex: number;
  onClose: () => void;
}> = ({ images, startIndex, onClose }) => {
  const [currentIndex, setCurrentIndex] = useState(startIndex);
  const [isLoaded, setIsLoaded] = useState(false);
  const [isClosing, setIsClosing] = useState(false);
  const [isSwiping, setIsSwiping] = useState(false);

  const imageToShow = images[currentIndex];
  // Next.js Image component handles different sizes automatically, no need for highResSrc replacement

  const handleClose = useCallback(() => {
    setIsClosing(true);
    setTimeout(onClose, 300); // Coincide con la duración de la animación de cierre
  }, [onClose]);

  const changeImage = useCallback(
    (direction: 'next' | 'prev') => {
      setIsSwiping(true);
      setIsLoaded(false);
      setTimeout(() => {
        if (direction === 'next') {
          setCurrentIndex(prev => (prev + 1) % images.length);
        } else {
          setCurrentIndex(prev => (prev - 1 + images.length) % images.length);
        }
        setIsSwiping(false);
      }, 150); // Mitad de la duración de la transición para un efecto suave
    },
    [images.length]
  );

  const goToNext = useCallback(() => changeImage('next'), [changeImage]);
  const goToPrev = useCallback(() => changeImage('prev'), [changeImage]);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') handleClose();
      if (e.key === 'ArrowRight') goToNext();
      if (e.key === 'ArrowLeft') goToPrev();
    };
    window.addEventListener('keydown', handleKeyDown);
    document.body.style.overflow = 'hidden'; // Evita el scroll del fondo
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'auto'; // Restaura el scroll
    };
  }, [handleClose, goToNext, goToPrev]);

  return (
    <div
      className={`fixed inset-0 z-[100] flex items-center justify-center bg-black bg-opacity-80 backdrop-blur-sm transition-opacity duration-300 ${isClosing ? 'opacity-0' : 'opacity-100'}`}
      onClick={handleClose}
      role="dialog"
      aria-modal="true"
      aria-label="Vista ampliada de la imagen"
    >
      <div
        className="relative w-full max-w-5xl h-full max-h-[90vh] p-2 sm:p-4 flex items-center justify-center"
        onClick={e => e.stopPropagation()}
      >
        <div
          className={`relative w-full h-full flex items-center justify-center transition-transform duration-300 ease-in-out ${isSwiping ? 'scale-95 opacity-0' : 'scale-100 opacity-100'}`}
        >
          {!isLoaded && (
            <div className="absolute w-12 h-12 border-4 border-slate-400 border-t-white rounded-full animate-spin z-0"></div>
          )}

          <Image
            key={imageToShow.id}
            src={imageToShow.src}
            alt={imageToShow.alt}
            className={`max-w-full max-h-full object-contain rounded-lg shadow-2xl transition-opacity duration-500 z-10 ${isLoaded ? 'opacity-100' : 'opacity-0'}`}
            onLoad={() => setIsLoaded(true)}
            fill // Use fill to make the image cover the parent div
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 60vw"
          />
        </div>

        <button
          onClick={handleClose}
          className="absolute top-2 right-2 sm:top-4 sm:right-4 text-white hover:text-slate-300 p-2 bg-black/40 rounded-full transition z-20"
          aria-label="Cerrar vista ampliada"
        >
          <CloseIcon className="w-6 h-6" />
        </button>
        <button
          onClick={goToPrev}
          className="absolute left-2 sm:left-4 top-1/2 -translate-y-1/2 text-white hover:text-slate-300 p-2 bg-black/40 rounded-full transition z-20"
          aria-label="Imagen anterior"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={2}
            stroke="currentColor"
            className="w-7 h-7"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M15.75 19.5 8.25 12l7.5-7.5"
            />
          </svg>
        </button>
        <button
          onClick={goToNext}
          className="absolute right-2 sm:right-4 top-1/2 -translate-y-1/2 text-white hover:text-slate-300 p-2 bg-black/40 rounded-full transition z-20"
          aria-label="Siguiente imagen"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={2}
            stroke="currentColor"
            className="w-7 h-7"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="m8.25 4.5 7.5 7.5-7.5 7.5"
            />
          </svg>
        </button>

        <div className="absolute bottom-4 left-4 right-4 text-center text-white p-2 bg-black/40 rounded">
          <p className="text-sm sm:text-base">{imageToShow.alt}</p>
        </div>
      </div>
    </div>
  );
};

// --- Componente Principal de la Página de Galería ---
interface GroupedGalleryImages {
  [key: string]: GalleryImage[];
}

export default function GalleryPage() {
  const [sectionRef, isVisible] = useScrollAnimation<HTMLDivElement>({
    threshold: 0.05,
  });
  const [lightboxState, setLightboxState] = useState<{
    images: GalleryImage[];
    index: number;
  } | null>(null);

  const openLightbox = (images: GalleryImage[], index: number) => {
    setLightboxState({ images, index });
  };
  const closeLightbox = () => setLightboxState(null);

  const groupedImages = GALLERY_IMAGES_DATA.reduce((acc, image) => {
    const massageType = image.massageType;
    if (!acc[massageType]) acc[massageType] = [];
    acc[massageType].push(image);
    return acc;
  }, {} as GroupedGalleryImages);

  const orderedTreatmentTypes = TREATMENTS_DATA.map(treatment => treatment.id);

  const imageGallerySchema = {
    "@context": "https://schema.org",
    "@type": "ImageGallery",
    "name": "Galería de Bienestar SPA Pascale",
    "description": "Imágenes de los ambientes, terapias y momentos de relajación en SPA Pascale.",
    "url": "https://www.spapascale.com/galeria", // Reemplazar con tu URL real
    "associatedMedia": GALLERY_IMAGES_DATA.map(image => ({
      "@type": "ImageObject",
      "contentUrl": image.src,
      "name": image.alt,
      "description": image.alt
    }))
  };

  return (
    <>
      <Script
        id="image-gallery-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(imageGallerySchema) }}
      />
      <div
        ref={sectionRef}
        className={`py-16 sm:py-24 bg-brand-light transition-all duration-700 ease-out transform ${
          isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-serif font-bold text-brand-primary sm:text-5xl">
              Galería de Bienestar
            </h1>
            <p className="mt-6 text-lg text-brand-text-light max-w-3xl mx-auto">
              Explora los ambientes y detalles que hemos preparado para tu
              experiencia. Haz clic en una imagen para verla en detalle.
            </p>
          </div>

          {orderedTreatmentTypes.map(massageTypeId => {
            const treatment = TREATMENTS_DATA.find(
              t => t.id === massageTypeId
            ) as Treatment | undefined;
            const imagesForType = groupedImages[massageTypeId] || [];

            if (!treatment || imagesForType.length === 0) return null;

            return (
              <section key={massageTypeId} className="mb-16">
                <h2 className="text-2xl sm:text-3xl font-serif font-semibold text-brand-dark mb-8 text-center">
                  {treatment.title}
                </h2>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 lg:gap-8">
                  {imagesForType.map((image, index) => (
                    <GalleryImageCard
                      key={image.id}
                      image={image}
                      onClick={() => openLightbox(imagesForType, index)}
                    />
                  ))}
                </div>
              </section>
            );
          })}
          {Object.keys(groupedImages).length === 0 && (
            <p className="text-center text-brand-text-light text-lg">
              Próximamente agregaremos imágenes a nuestra galería. ¡Vuelve
              pronto!
            </p>
          )}
        </div>
      </div>
      {lightboxState && (
        <Lightbox
          images={lightboxState.images}
          startIndex={lightboxState.index}
          onClose={closeLightbox}
        />
      )}
    </>
  );
}