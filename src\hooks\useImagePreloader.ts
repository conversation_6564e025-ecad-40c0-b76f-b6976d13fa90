import { useEffect, useState } from 'react';

interface PreloadOptions {
  priority?: boolean;
  timeout?: number;
}

export const useImagePreloader = (
  imageSources: string[],
  options: PreloadOptions = {}
) => {
  const [loadedImages, setLoadedImages] = useState<Set<string>>(new Set());
  const [isLoading, setIsLoading] = useState(true);
  const [errors, setErrors] = useState<Set<string>>(new Set());

  const { priority = false, timeout = 10000 } = options;

  useEffect(() => {
    if (!imageSources.length) {
      setIsLoading(false);
      return;
    }

    const preloadImage = (src: string): Promise<string> => {
      return new Promise((resolve, reject) => {
        const img = new Image();
        
        const timeoutId = setTimeout(() => {
          reject(new Error(`Timeout loading image: ${src}`));
        }, timeout);

        img.onload = () => {
          clearTimeout(timeoutId);
          resolve(src);
        };

        img.onerror = () => {
          clearTimeout(timeoutId);
          reject(new Error(`Failed to load image: ${src}`));
        };

        img.src = src;
      });
    };

    const preloadImages = async () => {
      setIsLoading(true);
      const newLoadedImages = new Set<string>();
      const newErrors = new Set<string>();

      // Si es prioritario, cargar en secuencia, sino en paralelo
      if (priority) {
        for (const src of imageSources) {
          try {
            await preloadImage(src);
            newLoadedImages.add(src);
          } catch (error) {
            console.warn('Error preloading image:', error);
            newErrors.add(src);
          }
        }
      } else {
        const results = await Promise.allSettled(
          imageSources.map(src => preloadImage(src))
        );

        results.forEach((result, index) => {
          const src = imageSources[index];
          if (result.status === 'fulfilled') {
            newLoadedImages.add(src);
          } else {
            console.warn('Error preloading image:', result.reason);
            newErrors.add(src);
          }
        });
      }

      setLoadedImages(newLoadedImages);
      setErrors(newErrors);
      setIsLoading(false);
    };

    preloadImages();
  }, [imageSources, priority, timeout]);

  return {
    loadedImages,
    isLoading,
    errors,
    isImageLoaded: (src: string) => loadedImages.has(src),
    hasError: (src: string) => errors.has(src),
    progress: imageSources.length > 0 ? loadedImages.size / imageSources.length : 1,
  };
};

// Hook específico para precargar imágenes críticas del spa
export const useSpaImagePreloader = () => {
  const criticalImages = [
    'https://picsum.photos/seed/forestSunbeams/1920/1080', // Hero principal
    'https://i.ibb.co/wh8ymY05/enhanced-Captura2.png', // Logo/imagen principal
  ];

  return useImagePreloader(criticalImages, { priority: true, timeout: 5000 });
};
