'use client';

import React from 'react';
import { Testimonial } from '../types';
import { StarRatingDisplay } from './StarRatingDisplay';

interface TestimonialCardProps {
  testimonial: Testimonial;
}

export const TestimonialCard: React.FC<TestimonialCardProps> = ({
  testimonial,
}) => {
  const avatarUrl = `https://picsum.photos/seed/${testimonial.avatarSeed}/100/100`;

  return (
    <div className="bg-white rounded-xl shadow-lg overflow-hidden flex flex-col p-6 transition-all duration-300 hover:shadow-2xl hover:scale-[1.03]">
      <div className="flex items-center mb-4">
        <img
          src={avatarUrl}
          alt={`Avatar de ${testimonial.name}`}
          className="w-14 h-14 rounded-full mr-4 object-cover border-2 border-brand-primary/50"
          loading="eager"
          fetchPriority="high"
          width="100"
          height="100"
        />
        <div>
          <h4 className="font-semibold text-brand-text text-lg">
            {testimonial.name}
          </h4>
          {testimonial.location && (
            <p className="text-xs text-brand-text-light">
              {testimonial.location}
            </p>
          )}
        </div>
      </div>
      <div className="mb-3">
        <StarRatingDisplay rating={testimonial.rating} />
      </div>
      <p className="text-brand-text-light text-sm leading-relaxed flex-grow">
        "{testimonial.testimonial}"
      </p>
    </div>
  );
};
