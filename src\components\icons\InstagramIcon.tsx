import React from 'react';

export const InstagramIcon: React.FC<React.SVGProps<SVGSVGElement>> = props => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    fill="currentColor"
    {...props}
  >
    <path d="M12 2c-2.72 0-3.05.01-4.12.06-1.06.05-1.79.24-2.42.52-.65.27-1.19.63-1.74 1.19-.56.56-.92 1.1-1.19 1.74-.28.63-.47 1.36-.52 2.42-.05 1.07-.06 1.4-.06 4.12s.01 3.05.06 4.12c.05 1.06.24 1.79.52 2.42.27.65.63 1.19 1.19 1.74.56.56 1.1.92 1.74 1.19.63.28 1.36.47 2.42.52 1.07.05 1.4.06 4.12.06s3.05-.01 4.12-.06c1.06-.05 1.79-.24 2.42-.52.65-.27 1.19-.63 1.74-1.19.56-.56.92-1.1 1.19-1.74.28-.63.47-1.36.52-2.42.05-1.07.06-1.4.06-4.12s-.01-3.05-.06-4.12c-.05-1.06-.24-1.79-.52-2.42-.27-.65-.63-1.19-1.19-1.74-.56-.56-1.1-.92-1.74-1.19-.63-.28-1.36-.47-2.42-.52C15.05 2.01 14.72 2 12 2zm0 1.8c2.65 0 2.95.01 4 .06 1.02.05 1.58.22 1.96.38.49.2.82.47 1.19.83.37.37.63.7.83 1.19.16.38.33.94.38 1.96.05 1.05.06 1.35.06 4s-.01 2.95-.06 4c-.05 1.02-.22 1.58-.38 1.96-.2.49-.47.82-.83 1.19-.37.37-.7.63-1.19.83-.38.16-.94.33-1.96.38-1.05.05-1.35.06-4 .06s-2.95-.01-4-.06c-1.02-.05-1.58-.22-1.96-.38-.49-.2-.82-.47-1.19-.83-.37-.37-.63-.7-.83-1.19-.16-.38-.33-.94-.38-1.96-.05-1.05-.06-1.35-.06-4s.01-2.95.06-4c.05-1.02.22-1.58.38-1.96.2-.49.47-.82.83-1.19.37-.37.7-.63-1.19-.83.38-.16.94-.33 1.96-.38 1.05-.05 1.35-.06 4-.06zm0 4.47c-2.37 0-4.29 1.92-4.29 4.29s1.92 4.29 4.29 4.29 4.29-1.92 4.29-4.29-1.92-4.29-4.29-4.29zm0 6.79c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5zm4.87-6.93c-.69 0-1.25-.56-1.25-1.25s.56-1.25 1.25-1.25 1.25.56 1.25 1.25-.56 1.25-1.25 1.25z" />
  </svg>
);
