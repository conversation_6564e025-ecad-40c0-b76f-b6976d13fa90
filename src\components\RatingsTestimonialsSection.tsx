'use client';

import React from 'react';
import { TESTIMONIALS_DATA } from '../constants';
import { TestimonialCard } from './TestimonialCard';
import { StarRatingDisplay } from './StarRatingDisplay';
import { useScrollAnimation } from '../hooks/useScrollAnimation';

export const RatingsTestimonialsSection: React.FC = () => {
  const [sectionRef, isVisible] = useScrollAnimation<HTMLElement>({
    threshold: 0.05,
  });

  if (!TESTIMONIALS_DATA || TESTIMONIALS_DATA.length === 0) {
    return null; // Don't render section if no testimonials
  }

  const averageRating =
    TESTIMONIALS_DATA.reduce((acc, t) => acc + t.rating, 0) /
    TESTIMONIALS_DATA.length;

  return (
    <section
      id="testimonios"
      ref={sectionRef}
      className={`py-16 sm:py-24 bg-white transition-all duration-700 ease-out transform ${
        isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
      }`}
    >
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-serif font-bold text-brand-primary sm:text-4xl">
            Testimonios que Inspiran
          </h2>
          <p className="mt-4 text-lg text-brand-text-light max-w-2xl mx-auto">
            Descubre lo que nuestros clientes dicen sobre su experiencia de
            bienestar.
          </p>
          {averageRating > 0 && (
            <div className="mt-6 flex flex-col items-center">
              <StarRatingDisplay
                rating={averageRating}
                starColor="text-yellow-400"
              />
              <p className="mt-2 text-md text-brand-text-light">
                Promedio de {averageRating.toFixed(1)} de 5 estrellas basado en{' '}
                {TESTIMONIALS_DATA.length} opiniones.
              </p>
            </div>
          )}
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {TESTIMONIALS_DATA.map(testimonial => (
            <TestimonialCard key={testimonial.id} testimonial={testimonial} />
          ))}
        </div>
      </div>
    </section>
  );
};
