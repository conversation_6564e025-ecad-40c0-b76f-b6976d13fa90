import { MetadataRoute } from 'next';
import { TREATMENTS_DATA } from '../constants';

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = 'https://www.spapascale.com'; // Reemplazar con tu dominio real

  const staticPages = [
    { url: `${baseUrl}/`, lastModified: new Date() },
    { url: `${baseUrl}/sobre-carolina`, lastModified: new Date() },
    { url: `${baseUrl}/tratamientos`, lastModified: new Date() },
    { url: `${baseUrl}/galeria`, lastModified: new Date() },
    { url: `${baseUrl}/contacto`, lastModified: new Date() },
    { url: `${baseUrl}/agenda`, lastModified: new Date() },
    // { url: `${baseUrl}/test`, lastModified: new Date() }, // Descomentar si quieres indexar la página de prueba
  ];

  const treatmentPages = TREATMENTS_DATA.map(treatment => ({
    url: `${baseUrl}/tratamientos#${treatment.id}`, // Enlaces a secciones dentro de la página de tratamientos
    lastModified: new Date(),
  }));

  return [...staticPages, ...treatmentPages];
}
