'use client';

import React from 'react';
import { TREATMENTS_DATA, TREATMENTS_INTRO_TEXT } from '../constants';
import { TreatmentCard } from './TreatmentCard';
import { useScrollAnimation } from '../hooks/useScrollAnimation';

export const TreatmentsSection: React.FC = () => {
  const [sectionRef, isVisible] = useScrollAnimation<HTMLElement>({
    threshold: 0.05,
  }); // Trigger a bit earlier

  return (
    <section
      id="tratamientos"
      ref={sectionRef}
      className={`py-16 sm:py-24 bg-brand-light transition-all duration-700 ease-out transform ${
        isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
      }`}
    >
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-serif font-bold text-brand-primary sm:text-4xl">
            Nuestros Tratamientos Terapéuticos
          </h2>
          <p className="mt-4 text-lg text-brand-text-light max-w-2xl mx-auto">
            {TREATMENTS_INTRO_TEXT}
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {TREATMENTS_DATA.map(treatment => (
            <TreatmentCard key={treatment.id} treatment={treatment} />
          ))}
        </div>
      </div>
    </section>
  );
};
