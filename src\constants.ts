import {
  NavLinkInfo,
  Treatment,
  Testimonial,
  CarouselImageItem as CarouselImageItemType,
  GalleryImage,
} from './types'; // Renamed import to avoid conflict
import { LeafIcon } from './components/icons/LeafIcon';
import React from 'react';

export const NAV_LINKS: NavLinkInfo[] = [
  { label: 'Inicio', path: '/' },
  { label: 'Sobre Mí', path: '/sobre-carolina' },
  { label: 'Tratamientos', path: '/tratamientos' },
  { label: 'Galer<PERSON>', path: '/galeria' },
  { label: 'Contacto', path: '/contacto' },
  { label: 'Agendar Cita', path: '/agenda', isButton: true }, // Updated path
];

export const ABOUT_ME_TEXT = {
  greeting: '¡Bienvenida a un espacio dedicado a tu bienestar!',
  introduction:
    'So<PERSON> <PERSON>, Cosmetóloga Titulada y terapeuta corporal, apasionada por la conexión profunda entre la salud, la belleza y el equilibrio emocional. Mi misión es ofrecerte un refugio donde puedas desconectar del ritmo acelerado del día a día y reconectar contigo misma a través de terapias manuales expertas.',
  philosophy:
    'Con una base sólida en cosmetología, entiendo la piel y el cuerpo como un mapa que refleja nuestro estado interior. Por eso, cada uno de mis servicios está diseñado no solo para embellecer, sino para sanar desde adentro hacia afuera, combinando conocimiento técnico con una profunda vocación por el cuidado holístico de la persona.',
};

export const TREATMENTS_INTRO_TEXT =
  'Cada masaje es una experiencia única, adaptada a tus necesidades específicas. Utilizo productos de alta calidad y un enfoque personalizado para garantizar que recibas exactamente el cuidado que tu cuerpo y mente necesitan.';

export const TREATMENTS_DATA: Treatment[] = [
  {
    id: 'relax',
    title: 'Masaje de Relajación Profunda',
    shortDescription: 'Enfoque Mente-Cuerpo para una calma total.',
    longDescription:
      'Más que un masaje, es un viaje sensorial diseñado para liberar el estrés acumulado a nivel físico y mental. Esta terapia combina maniobras suaves y envolventes con un enfoque en el equilibrio energético, inspirado en disciplinas como el Reiki, para calmar el sistema nervioso y silenciar el ruido mental. Es la experiencia ideal para quienes buscan una pausa reparadora y una profunda sensación de paz.',
    benefits: [
      'Reducción drástica de los niveles de estrés y ansiedad.',
      'Mejora de la calidad del sueño.',
      'Alivio de la fatiga mental y aumento de la claridad.',
      'Relajación profunda de la musculatura general.',
    ],
    icon: React.createElement(LeafIcon),
  },
  {
    id: 'decontracting',
    title: 'Masaje Descontracturante Terapéutico',
    shortDescription: 'Alivio para tensión crónica y "nudos" musculares.',
    longDescription:
      'Diseñado específicamente para aliviar el dolor y liberar la tensión crónica alojada en las capas profundas de los músculos. Mediante técnicas de presión focalizada y maniobras precisas, trabajamos sobre "nudos" (contracturas) causados por el estrés, malas posturas o sobrecarga física. Recupera tu movilidad y vive sin las molestias de la tensión muscular.',
    benefits: [
      'Alivio efectivo de dolores de espalda, cuello y hombros.',
      'Aumento de la flexibilidad y el rango de movimiento.',
      'Disminución de la rigidez muscular.',
      'Mejora de la circulación en las zonas afectadas.',
    ],
    icon: React.createElement(LeafIcon),
  },
  {
    id: 'lymphatic',
    title: 'Drenaje Linfático Manual',
    shortDescription: 'Técnica suave para desintoxicar y fortalecer.',
    longDescription:
      'Esta es una técnica de masaje suave, rítmica y precisa, orientada a estimular el funcionamiento del sistema linfático. Es fundamental para eliminar toxinas, reducir la retención de líquidos (edema) y fortalecer el sistema inmunológico. Es un tratamiento altamente recomendado para procesos postoperatorios, piernas cansadas y como un poderoso detox corporal.',
    benefits: [
      'Reducción de la hinchazón y la retención de líquidos.',
      'Potente efecto desintoxicante y depurativo.',
      'Mejora de la circulación y la salud de la piel.',
      'Fortalecimiento del sistema inmune.',
    ],
    icon: React.createElement(LeafIcon),
  },
];

export const GALLERY_IMAGES_DATA: GalleryImage[] = [
  // Masaje de Relajación Profunda
  {
    id: 'relax-1',
    src: 'https://picsum.photos/seed/spaCandles/600/400',
    alt: 'Velas aromáticas y flores en un ambiente de spa sereno',
    massageType: 'relax',
  },
  {
    id: 'relax-2',
    src: 'https://picsum.photos/seed/zenStonesWater/600/400',
    alt: 'Piedras zen apiladas cerca de agua tranquila',
    massageType: 'relax',
  },
  {
    id: 'relax-3',
    src: 'https://picsum.photos/seed/peacefulView/600/400',
    alt: 'Persona relajándose con una vista pacífica',
    massageType: 'relax',
  },
  {
    id: 'relax-4',
    src: 'https://picsum.photos/seed/massageOilFlower/600/400',
    alt: 'Aceite de masaje con una flor delicada',
    massageType: 'relax',
  },
  {
    id: 'relax-5',
    src: 'https://picsum.photos/seed/softTowels/600/400',
    alt: 'Toallas suaves y esponjosas apiladas',
    massageType: 'relax',
  },
  {
    id: 'relax-6',
    src: 'https://picsum.photos/seed/incenseSmoke/600/400',
    alt: 'Humo de incienso creando una atmósfera calmante',
    massageType: 'relax',
  },

  // Masaje Descontracturante Terapéutico
  {
    id: 'decontracting-1',
    src: 'https://picsum.photos/seed/backMassageTherapy/600/400',
    alt: 'Primer plano de un masaje de espalda terapéutico',
    massageType: 'decontracting',
  },
  {
    id: 'decontracting-2',
    src: 'https://picsum.photos/seed/muscleAnatomyChart/600/400',
    alt: 'Gráfico anatómico de músculos para enfoque terapéutico',
    massageType: 'decontracting',
  },
  {
    id: 'decontracting-3',
    src: 'https://picsum.photos/seed/therapistHandsFocus/600/400',
    alt: 'Manos de terapeuta trabajando en un punto de tensión',
    massageType: 'decontracting',
  },
  {
    id: 'decontracting-4',
    src: 'https://picsum.photos/seed/stretchingPose/600/400',
    alt: 'Persona realizando un estiramiento para aliviar tensión',
    massageType: 'decontracting',
  },
  {
    id: 'decontracting-5',
    src: 'https://picsum.photos/seed/hotStonesBack/600/400',
    alt: 'Piedras calientes colocadas en la espalda para alivio muscular',
    massageType: 'decontracting',
  },
  {
    id: 'decontracting-6',
    src: 'https://picsum.photos/seed/focusedPressurePoint/600/400',
    alt: 'Aplicación de presión en un punto específico del músculo',
    massageType: 'decontracting',
  },

  // Drenaje Linfático Manual
  {
    id: 'lymphatic-1',
    src: 'https://picsum.photos/seed/lightWaterFlow/600/400',
    alt: 'Flujo de agua suave y claro simbolizando el drenaje linfático',
    massageType: 'lymphatic',
  },
  {
    id: 'lymphatic-2',
    src: 'https://picsum.photos/seed/gentleTouchSkin/600/400',
    alt: 'Toque suave sobre la piel, representando la delicadeza del drenaje',
    massageType: 'lymphatic',
  },
  {
    id: 'lymphatic-3',
    src: 'https://picsum.photos/seed/bodyContourSilhouette/600/400',
    alt: 'Silueta corporal mostrando contornos suaves después del drenaje',
    massageType: 'lymphatic',
  },
  {
    id: 'lymphatic-4',
    src: 'https://picsum.photos/seed/healthyLifestyleFruits/600/400',
    alt: 'Frutas frescas y agua, simbolizando desintoxicación y salud',
    massageType: 'lymphatic',
  },
  {
    id: 'lymphatic-5',
    src: 'https://picsum.photos/seed/clearSkinTexture/600/400',
    alt: 'Textura de piel clara y saludable',
    massageType: 'lymphatic',
  },
  {
    id: 'lymphatic-6',
    src: 'https://picsum.photos/seed/lymphaticSystemDiagram/600/400',
    alt: 'Representación estilizada del sistema linfático',
    massageType: 'lymphatic',
  },
];

export const CALL_TO_ACTION_TEXT = {
  main: 'Tu bienestar es mi prioridad. Te invito a regalarte una pausa, a escuchar a tu cuerpo y a redescubrir la armonía interior.',
  button:
    'Agenda tu cita hoy mismo y comienza tu viaje hacia una vida más equilibrada y serena.',
};

export const FOOTER_TEXT = `© ${new Date().getFullYear()} Carolina Paz Gómez Garín. Todos los derechos reservados.`;

// Note: The interface CarouselImageItem was moved to types.ts and imported as CarouselImageItemType
// The export below this comment block should be removed or updated if constants.ts previously defined its own CarouselImageItem.
// Assuming it was a duplicate and types.ts is the source of truth, this export is redundant.

// export interface CarouselImageItem { // This was likely a duplicate or an old definition
//   id: string;
//   alt: string;
//   src: string;
// }

export const CAROUSEL_IMAGES: CarouselImageItemType[] = [
  {
    id: 'forest-sunbeams',
    alt: 'Bosque frondoso con rayos de sol penetrando las hojas',
    src: 'https://picsum.photos/seed/forestSunbeams/1920/1080',
    width: 1920,
    height: 1080,
  },
  {
    id: 'zen-garden',
    alt: 'Jardín Zen sereno con arena rastrillada y piedras cuidadosamente colocadas',
    src: 'https://picsum.photos/seed/zenGarden/1920/1080',
  },
  {
    id: 'mountain-stream',
    alt: 'Arroyo tranquilo fluyendo entre rocas en un paisaje montañoso',
    src: 'https://picsum.photos/seed/mountainStream/1920/1080',
  },
  {
    id: 'lavender-field',
    alt: 'Extenso campo de lavanda en flor bajo un cielo azul',
    src: 'https://picsum.photos/seed/lavenderField/1920/1080',
  },
  {
    id: 'crystal-lake',
    alt: 'Lago de aguas cristalinas reflejando montañas imponentes',
    src: 'https://picsum.photos/seed/crystalLake/1920/1080',
  },
  {
    id: 'spa-stones',
    alt: 'Piedras de basalto apiladas en equilibrio junto a flores de frangipani y tallos de bambú, evocando serenidad.',
    src: 'https://picsum.photos/seed/spaStonesFrangipaniBamboo/1920/1080',
  },
  {
    id: 'wildflower-meadow',
    alt: 'Pradera vibrante llena de flores silvestres de múltiples colores',
    src: 'https://picsum.photos/seed/wildflowerMeadow/1920/1080',
  },
  {
    id: 'zen-garden-stones',
    alt: 'Jardín Zen minimalista con arena rastrillada en patrones ondulantes y rocas cuidadosamente dispuestas, inspirando calma y meditación.',
    src: 'https://picsum.photos/seed/zenGardenStones/1920/1080',
  },
  {
    id: 'bamboo-forest',
    alt: 'Denso bosque de bambú con altos tallos verdes creando un túnel natural',
    src: 'https://picsum.photos/seed/bambooForest/1920/1080',
  },
  {
    id: 'rolling-hills',
    alt: 'Colinas verdes y onduladas extendiéndose hasta el horizonte',
    src: 'https://picsum.photos/seed/rollingHills/1920/1080',
  },
  {
    id: 'serene-creek-forest',
    alt: 'Riachuelo sereno fluyendo suavemente sobre rocas cubiertas de musgo en un bosque verde y exuberante.',
    src: 'https://picsum.photos/seed/sereneCreekForest/1920/1080',
  },
  {
    id: 'snowy-landscape',
    alt: 'Paisaje nevado y silencioso con árboles cubiertos de nieve',
    src: 'https://picsum.photos/seed/snowyLandscape/1920/1080',
  },
  {
    id: 'japanese-garden',
    alt: 'Jardín japonés tradicional con estanque sereno, peces koi y puentes de madera',
    src: 'https://picsum.photos/seed/japaneseGarden/1920/1080',
  },
  {
    id: 'hilltop-sunrise',
    alt: 'Amanecer dorado visto desde la cima de una colina, iluminando el paisaje',
    src: 'https://picsum.photos/seed/hilltopSunrise/1920/1080',
  },
  {
    id: 'misty-valley',
    alt: 'Valle cubierto de una densa niebla al amanecer, con picos de montañas emergiendo',
    src: 'https://picsum.photos/seed/mistyValley/1920/1080',
  },
];

export const TESTIMONIALS_DATA: Testimonial[] = [
  {
    id: 'testimonial-1',
    name: 'Sofía R.',
    avatarSeed: 'sofiaR',
    location: 'Santiago, Chile',
    rating: 5,
    testimonial:
      '¡Una experiencia increíble! Carolina tiene manos mágicas. Salí completamente renovada y sin tensiones. El ambiente es muy acogedor y profesional. ¡Totalmente recomendado!',
  },
  {
    id: 'testimonial-2',
    name: 'Martín L.',
    avatarSeed: 'martinL',
    location: 'Viña del Mar, Chile',
    rating: 4.5,
    testimonial:
      'El masaje descontracturante fue justo lo que necesitaba. Carolina identificó todos mis puntos de dolor y trabajó en ellos con mucha dedicación. Sentí un alivio inmediato.',
  },
  {
    id: 'testimonial-3',
    name: 'Laura V.',
    avatarSeed: 'lauraV',
    rating: 5,
    testimonial:
      'Me hice un drenaje linfático y los resultados fueron notorios. Además, Carolina es muy amable y explica todo el proceso. El lugar es un oasis de tranquilidad. Volveré sin duda.',
  },
  {
    id: 'testimonial-4',
    name: 'Javier P.',
    avatarSeed: 'javierP',
    location: 'Valparaíso, Chile',
    rating: 4,
    testimonial:
      'Buen masaje de relajación, me ayudó mucho con el estrés del trabajo. El trato fue muy profesional y el ambiente es perfecto para desconectar. Quizás un poco más de presión la próxima vez.',
  },
  {
    id: 'testimonial-5',
    name: 'Isabella C.',
    avatarSeed: 'isabellaC',
    location: 'Quilpué, Chile',
    rating: 5,
    testimonial:
      'Nunca había sentido una conexión tan profunda entre cuerpo y mente. El masaje de relajación fue más que una terapia, fue una experiencia transformadora. Carolina tiene un don especial para crear un espacio seguro y sanador. Salí flotando y con una claridad mental que no sentía hace años. ¡Imperdible!',
  },
  {
    id: 'testimonial-6',
    name: 'Andrés G.',
    avatarSeed: 'andresG',
    location: 'Concón, Chile',
    rating: 5,
    testimonial:
      'Después de una lesión deportiva, pensé que el dolor de espalda sería crónico. El masaje descontracturante terapéutico no solo alivió el dolor, sino que me devolvió la movilidad. Carolina es una profesional excepcional, su conocimiento del cuerpo es impresionante. Ha sido clave en mi recuperación.',
  },
];

export const WHATSAPP_PHONE_NUMBER = '56977509997'; // Replace with actual number in international format without '+' or spaces
export const WHATSAPP_GREETING_MESSAGE =
  'Hola! me gustaria saber mas sobre los servicios del SPA Pascale y horarios';