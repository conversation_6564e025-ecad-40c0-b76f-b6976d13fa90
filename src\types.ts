import React from 'react';

export interface NavLinkInfo {
  label: string;
  path: string;
  isButton?: boolean;
}

export interface Treatment {
  id: string;
  title: string;
  shortDescription: string;
  longDescription: string;
  benefits: string[];
  icon?: React.ReactElement<{ className?: string }>;
}

export interface CarouselImageItem {
  id: string;
  alt: string;
  src: string;
  width?: number;
  height?: number;
}

export interface Testimonial {
  id: string;
  name: string;
  avatarSeed: string; // For generating a unique Picsum avatar
  location?: string;
  rating: number; // e.g., 4.5
  testimonial: string;
}

export interface GalleryImage {
  id: string;
  src: string;
  alt: string;
  massageType: 'relax' | 'decontracting' | 'lymphatic'; // Corresponds to Treatment IDs
}
