# DOCUMENTACIÓN DE ESTRUCTURA DEL PROYECTO - SPA PASCALE

## Información del Documento
**Proyecto:** SPA Pascale - Bienestar Integral  
**Fecha de creación:** 25 de Julio de 2025  
**Versión:** 1.0  
**Responsable:** Agente de IA Senior  

---

## 1. ESTRUCTURA GENERAL DEL PROYECTO

```
spa-pascale/
├── 📁 components/              # Componentes React reutilizables
│   ├── 📁 icons/              # Iconos personalizados
│   ├── AboutMeSection.tsx     # Sección "Sobre Carolina"
│   ├── CallToActionSection.tsx # Sección de llamada a la acción
│   ├── DateTimeDisplay.tsx    # Componente de fecha/hora
│   ├── FloatingParticles.tsx  # Partículas animadas de fondo
│   ├── Footer.tsx             # Pie de página
│   ├── Hero.tsx               # Sección principal con carrusel
│   ├── IntroScreen.tsx        # Pantalla de introducción
│   ├── LoadingSpinner.tsx     # Indicador de carga
│   ├── Navbar.tsx             # Barra de navegación
│   ├── OptimizedImage.tsx     # Componente de imagen optimizada
│   ├── RatingsTestimonialsSection.tsx # Sección de testimonios
│   ├── StarRatingDisplay.tsx  # Visualización de calificaciones
│   ├── TestimonialCard.tsx    # Tarjeta individual de testimonio
│   ├── TreatmentCard.tsx      # Tarjeta de tratamiento
│   ├── TreatmentsSection.tsx  # Sección de tratamientos
│   └── WhatsAppChatButton.tsx # Botón flotante de WhatsApp
├── 📁 pages/                  # Páginas de la aplicación
│   ├── AboutPage.tsx          # Página "Sobre Carolina"
│   ├── AgendaPage.tsx         # Página de reservas
│   ├── ContactPage.tsx        # Página de contacto
│   ├── GalleryPage.tsx        # Página de galería
│   ├── HomePage.tsx           # Página principal
│   └── ServicesPage.tsx       # Página de servicios
├── 📁 hooks/                  # Hooks personalizados
│   ├── useImagePreloader.ts   # Hook para preloading de imágenes
│   └── useScrollAnimation.ts  # Hook para animaciones de scroll
├── 📁 services/               # Servicios y lógica de negocio
│   └── emailService.ts        # Servicio de envío de emails
├── 📁 images/                 # Assets de imágenes organizados
│   ├── 📁 Carito/            # Imágenes de Carolina
│   ├── 📁 Clientes/          # Imágenes de clientes/testimonios
│   └── 📁 Logo/              # Logos y branding
├── 📁 public/                 # Archivos estáticos públicos
│   ├── _headers               # Headers de seguridad (Netlify)
│   ├── _redirects             # Configuración de redirects
│   ├── favicon.svg            # Icono del sitio
│   ├── manifest.json          # Manifiesto PWA
│   ├── robots.txt             # Configuración para crawlers
│   └── sitemap.xml            # Mapa del sitio para SEO
├── 📁 dist/                   # Build de producción
├── 📁 docs/                   # Documentación del proyecto
├── 📁 node_modules/           # Dependencias instaladas
├── App.tsx                    # Componente raíz de la aplicación
├── constants.ts               # Constantes y datos del proyecto
├── types.ts                   # Definiciones de tipos TypeScript
├── index.tsx                  # Punto de entrada de la aplicación
├── index.css                  # Estilos globales
├── index.html                 # Template HTML principal
├── package.json               # Configuración del proyecto y dependencias
├── tsconfig.json              # Configuración de TypeScript
├── vite.config.ts             # Configuración de Vite
├── eslint.config.js           # Configuración de ESLint
├── vercel.json                # Configuración para deployment en Vercel
└── rules.md                   # Reglas y directrices del proyecto
```

## 2. ARQUITECTURA DE COMPONENTES

### 2.1 Jerarquía de Componentes

```
App.tsx
├── IntroScreen.tsx (condicional)
├── FloatingParticles.tsx
├── Navbar.tsx
├── Routes/
│   ├── HomePage.tsx
│   │   ├── Hero.tsx
│   │   │   └── OptimizedImage.tsx
│   │   ├── AboutMeSection.tsx
│   │   ├── TreatmentsSection.tsx
│   │   │   └── TreatmentCard.tsx (×3)
│   │   ├── RatingsTestimonialsSection.tsx
│   │   │   ├── StarRatingDisplay.tsx
│   │   │   └── TestimonialCard.tsx (×6)
│   │   └── CallToActionSection.tsx
│   ├── AboutPage.tsx
│   ├── ServicesPage.tsx
│   ├── GalleryPage.tsx
│   │   └── OptimizedImage.tsx (×18)
│   ├── ContactPage.tsx
│   └── AgendaPage.tsx
├── Footer.tsx
└── WhatsAppChatButton.tsx
```

### 2.2 Patrones de Diseño Implementados

#### 2.2.1 Composition Pattern
- Componentes pequeños y especializados
- Reutilización a través de composición
- Separación clara de responsabilidades

#### 2.2.2 Container/Presentational Pattern
- **Containers:** Pages (lógica y estado)
- **Presentational:** Components (UI pura)
- Separación entre lógica y presentación

#### 2.2.3 Custom Hooks Pattern
- Lógica reutilizable extraída en hooks
- Separación de concerns
- Testabilidad mejorada

## 3. ORGANIZACIÓN DE ARCHIVOS

### 3.1 Convenciones de Nomenclatura

#### Componentes React
- **Formato:** PascalCase con extensión `.tsx`
- **Ejemplo:** `TreatmentCard.tsx`, `AboutMeSection.tsx`
- **Regla:** Nombres descriptivos que indican funcionalidad

#### Hooks Personalizados
- **Formato:** camelCase iniciando con `use` y extensión `.ts`
- **Ejemplo:** `useImagePreloader.ts`, `useScrollAnimation.ts`
- **Regla:** Prefijo `use` obligatorio para hooks

#### Servicios
- **Formato:** camelCase con sufijo `Service` y extensión `.ts`
- **Ejemplo:** `emailService.ts`
- **Regla:** Funcionalidad específica por archivo

#### Páginas
- **Formato:** PascalCase con sufijo `Page` y extensión `.tsx`
- **Ejemplo:** `HomePage.tsx`, `ContactPage.tsx`
- **Regla:** Una página por archivo

### 3.2 Estructura de Carpetas por Funcionalidad

#### `/components/` - Componentes Reutilizables
- **Propósito:** Componentes UI puros y reutilizables
- **Criterio:** Componentes que pueden usarse en múltiples páginas
- **Subcarpetas:** `/icons/` para iconos personalizados

#### `/pages/` - Páginas de la Aplicación
- **Propósito:** Componentes de nivel de página con routing
- **Criterio:** Componentes que representan rutas específicas
- **Responsabilidad:** Composición de componentes y gestión de estado de página

#### `/hooks/` - Lógica Reutilizable
- **Propósito:** Custom hooks para lógica compartida
- **Criterio:** Lógica que puede reutilizarse entre componentes
- **Responsabilidad:** Encapsulación de lógica de estado y efectos

#### `/services/` - Lógica de Negocio
- **Propósito:** Servicios y utilidades de negocio
- **Criterio:** Lógica que no depende de React
- **Responsabilidad:** Comunicación con APIs externas y procesamiento de datos

## 4. FLUJO DE DATOS

### 4.1 Gestión de Estado
```
Constants.ts (datos estáticos)
    ↓
Components (props)
    ↓
Local State (useState)
    ↓
Effects (useEffect)
    ↓
Custom Hooks (lógica reutilizable)
```

### 4.2 Comunicación Entre Componentes

#### Props Down
- Datos fluyen de padres a hijos vía props
- Configuración y datos estáticos desde `constants.ts`
- Tipos definidos en `types.ts` para type safety

#### Events Up
- Eventos de usuario manejados localmente
- Callbacks para comunicación padre-hijo
- Custom hooks para lógica compartida

## 5. CONFIGURACIÓN DE BUILD

### 5.1 Estructura de Build (`/dist/`)
```
dist/
├── assets/                    # Assets optimizados
│   ├── index-[hash].js       # JavaScript principal
│   ├── react-[hash].js       # Chunk de React
│   ├── router-[hash].js      # Chunk de React Router
│   └── index-[hash].css      # CSS optimizado
├── _headers                   # Headers de seguridad
├── _redirects                 # Configuración de redirects
├── index.html                 # HTML principal optimizado
├── favicon.svg                # Favicon
├── manifest.json              # Manifiesto PWA
├── robots.txt                 # SEO configuration
└── sitemap.xml                # Sitemap para SEO
```

### 5.2 Optimizaciones de Build
- **Code Splitting:** Separación automática de chunks
- **Tree Shaking:** Eliminación de código no utilizado
- **Minificación:** Compresión de JavaScript y CSS
- **Asset Optimization:** Optimización de imágenes y recursos

## 6. CONVENCIONES DE CÓDIGO

### 6.1 TypeScript
- **Strict mode:** Habilitado para máxima type safety
- **Interfaces:** Definidas en `types.ts` para reutilización
- **Props typing:** Todas las props tipadas explícitamente

### 6.2 React
- **Functional Components:** Exclusivamente componentes funcionales
- **Hooks:** Uso de hooks nativos y personalizados
- **JSX:** Sintaxis JSX con TypeScript

### 6.3 CSS
- **Módulos CSS:** Estilos modulares y scoped
- **Responsive:** Mobile-first approach
- **Accesibilidad:** Cumplimiento de estándares WCAG

## 7. ESCALABILIDAD

### 7.1 Preparación para Crecimiento
- **Estructura modular:** Fácil adición de nuevos componentes
- **Separación de concerns:** Lógica separada de presentación
- **Type safety:** TypeScript para refactoring seguro
- **Testing ready:** Estructura preparada para testing

### 7.2 Puntos de Extensión
- **Nuevos servicios:** Agregar en `/services/`
- **Nuevos componentes:** Agregar en `/components/`
- **Nuevas páginas:** Agregar en `/pages/` y routing
- **Nueva lógica:** Extraer en custom hooks

---

**Última actualización:** 25 de Julio de 2025  
**Próxima revisión:** 1 de Agosto de 2025
