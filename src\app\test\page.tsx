import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Página de Prueba | SPA Pascale',
  description: 'Esta es una página de prueba para verificar la funcionalidad de Next.js.',
  alternates: {
    canonical: 'https://www.spapascale.com/test',
  },
};

export default function TestPage() {
  return (
    <div className="py-16 sm:py-24 bg-red-100">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <h1 className="text-4xl font-bold text-red-600 text-center">
          TEST PAGE - ESTA ES UNA PÁGINA DE PRUEBA
        </h1>
        <p className="text-center mt-4 text-red-800">
          Si ves este contenido, significa que Next.js App Router está funcionando correctamente.
        </p>
      </div>
    </div>
  );
}
