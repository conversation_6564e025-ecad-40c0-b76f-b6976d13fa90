'use client';

import React, { useState, useEffect } from 'react';
import { WhatsAppIcon } from './icons/WhatsAppIcon';

interface WhatsAppChatButtonProps {
  phoneNumber: string;
  message: string;
}

export const WhatsAppChatButton: React.FC<WhatsAppChatButtonProps> = ({
  phoneNumber,
  message,
}) => {
  const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;

  return (
    <a
      href={whatsappUrl}
      target="_blank"
      rel="noopener noreferrer"
      className="fixed bottom-6 right-6 bg-green-500 hover:bg-green-600 text-white p-5 rounded-full shadow-xl hover:shadow-2xl transition-all duration-300 ease-in-out transform hover:scale-110 z-30 animate-subtle-pulse"
      aria-label="Chatear por WhatsApp"
      title="Chatear por WhatsApp"
    >
      <WhatsAppIcon className="w-8 h-8" />
    </a>
  );
};