'use client';

import { useEffect, useRef, useState, RefObject } from 'react';

interface ScrollAnimationOptions {
  threshold?: number;
  rootMargin?: string;
  triggerOnce?: boolean;
}

export const useScrollAnimation = <T extends HTMLElement>(
  options?: ScrollAnimationOptions
): [RefObject<T | null>, boolean] => {
  const [isVisible, setIsVisible] = useState(false);
  const elementRef = useRef<T | null>(null);

  const defaultOptions: IntersectionObserverInit = {
    threshold: options?.threshold ?? 0.1, // Trigger when 10% of the element is visible
    rootMargin: options?.rootMargin ?? '0px',
  };

  useEffect(() => {
    const currentElement = elementRef.current; // Capture current value for cleanup

    const observer = new IntersectionObserver((entries, obs) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          // Default to triggerOnce = true. If triggerOnce is explicitly false, don't unobserve.
          if (options?.triggerOnce !== false && currentElement) {
            obs.unobserve(currentElement);
          }
        } else {
          // If triggerOnce is false, allow element to become hidden again
          if (options?.triggerOnce === false) {
            setIsVisible(false);
          }
        }
      });
    }, defaultOptions);

    if (currentElement) {
      observer.observe(currentElement);
    }

    return () => {
      if (currentElement) {
        observer.unobserve(currentElement);
      }
    };
    // Rerun effect if options or elementRef changes.
    // Note: elementRef.current changes don't trigger re-render, this dependency is for the ref object itself.
  }, [options?.threshold, options?.rootMargin, options?.triggerOnce]);

  return [elementRef, isVisible];
};
