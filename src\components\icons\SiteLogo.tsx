import React from 'react';

export const SiteLogo: React.FC<React.SVGProps<SVGSVGElement>> = props => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 160 50" // Adjusted for a text-centric logo
    aria-labelledby="siteLogoTitle"
    {...props}
  >
    <title id="siteLogoTitle">Pascale SPA Logo</title>
    <text
      x="80" // Horizontal center of viewBox (160/2)
      y="18" // Adjusted y-coordinate for vertical centering of "PASCALE"
      fontFamily="Playfair Display, serif"
      fontSize="26" // Prominent font size for "PASCALE"
      fontWeight="700" // Bold
      textAnchor="middle"
      dominantBaseline="middle" // Ensures y is the true vertical center
      fill="currentColor"
    >
      PASCALE
    </text>
    <text
      x="80" // Horizontal center of viewBox (160/2)
      y="39.5" // Adjusted y-coordinate for "SPA", below "PASCALE"
      fontFamily="Inter, sans-serif"
      fontSize="13" // Smaller, complementary font size for "SPA"
      fontWeight="400" // Regular weight
      letterSpacing="0.1em" // Adds a refined, airy look to "SPA"
      textAnchor="middle"
      dominantBaseline="middle" // Ensures y is the true vertical center
      fill="currentColor"
    >
      SPA
    </text>
  </svg>
);
