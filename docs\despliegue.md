# DOCUMENTACIÓN DE DESPLIEGUE - SPA PASCALE

## Información del Documento
**Proyecto:** SPA Pascale - Bienestar Integral  
**Fecha de creación:** 25 de Julio de 2025  
**Versión:** 1.0  
**Responsable:** Agente de IA Senior  

---

## 1. ESTRATEGIA DE DESPLIEGUE

### 1.1 Entornos de Despliegue
- **Desarrollo Local:** Entorno de desarrollo individual
- **Staging:** Entorno de pruebas pre-producción
- **Producción:** Entorno live para usuarios finales

### 1.2 Plataformas Soportadas
- **Firebase Hosting:** Plataforma principal (ACTIVA)
- **Netlify:** Alternativa configurada
- **Vercel:** Alternativa configurada
- **GitHub Pages:** Opción de backup
- **Hosting tradicional:** Con configuración manual

## 2. DESPLIEGUE EN FIREBASE HOSTING (ACTIVO)

### 2.1 Configuración Actual

#### Estado del Proyecto Firebase
- **Proyecto:** pascale-spa
- **URL de producción:** https://pascale-spa.web.app
- **Console Firebase:** https://console.firebase.google.com/project/pascale-spa/overview
- **Estado:** ✅ ACTIVO Y FUNCIONAL

#### Configuración Implementada

##### firebase.json
```json
{
  "hosting": {
    "public": "dist",
    "ignore": [
      "firebase.json",
      "**/.*",
      "**/node_modules/**"
    ],
    "rewrites": [
      {
        "source": "**",
        "destination": "/index.html"
      }
    ],
    "headers": [
      {
        "source": "**/*.@(js|css)",
        "headers": [
          {
            "key": "Cache-Control",
            "value": "max-age=********"
          }
        ]
      },
      {
        "source": "**",
        "headers": [
          {
            "key": "X-Frame-Options",
            "value": "DENY"
          },
          {
            "key": "X-Content-Type-Options",
            "value": "nosniff"
          },
          {
            "key": "X-XSS-Protection",
            "value": "1; mode=block"
          },
          {
            "key": "Referrer-Policy",
            "value": "strict-origin-when-cross-origin"
          }
        ]
      }
    ]
  }
}
```

##### .firebaserc
```json
{
  "projects": {
    "default": "pascale-spa"
  }
}
```

### 2.2 Comandos de Deploy Firebase

#### Deploy Completo
```bash
# Build de producción
npm run build

# Deploy a Firebase Hosting
firebase deploy --only hosting
```

#### Deploy con Verificación
```bash
# Verificar proyecto activo
firebase use

# Deploy con confirmación
firebase deploy --only hosting --confirm
```

### 2.3 Verificación Post-Deploy

#### Checklist de Verificación
- [ ] Sitio accesible en https://pascale-spa.web.app
- [ ] Todas las rutas funcionando (6 páginas)
- [ ] Formularios operativos (contacto y reservas)
- [ ] WhatsApp integration funcional
- [ ] Responsive design verificado
- [ ] Performance dentro de objetivos

#### Métricas Actuales (25 Jul 2025)
- **Load Time:** 2.01s ✅
- **First Paint:** 1.79s ✅
- **Funcionalidades:** 100% operativas ✅
- **Error Rate:** 0% ✅

## 3. DESPLIEGUE EN NETLIFY (ALTERNATIVO)

### 2.1 Configuración Inicial

#### Paso 1: Preparación del Repositorio
```bash
# Asegurar que el proyecto está en GitHub
git remote -v
# origin  https://github.com/usuario/spa-pascale.git

# Verificar que netlify.toml está en la raíz
ls -la netlify.toml
```

#### Paso 2: Crear Cuenta y Sitio en Netlify
1. **Crear cuenta:** https://netlify.com
2. **Conectar GitHub:** Autorizar acceso al repositorio
3. **Crear nuevo sitio:** "New site from Git"
4. **Seleccionar repositorio:** spa-pascale
5. **Configurar build:**
   - Build command: `npm run build`
   - Publish directory: `dist`
   - Branch: `main`

#### Paso 3: Configurar Variables de Entorno
```bash
# En Netlify Dashboard → Site Settings → Environment Variables
VITE_EMAILJS_SERVICE_ID=service_xxxxxxx
VITE_EMAILJS_TEMPLATE_ID=template_xxxxxxx
VITE_EMAILJS_PUBLIC_KEY=xxxxxxxxxxxxxxx
NODE_VERSION=18
```

### 2.2 Configuración Avanzada

#### netlify.toml (Ya configurado)
```toml
[build]
  publish = "dist"
  command = "npm run build"

[build.environment]
  NODE_VERSION = "18"

# SPA Redirects
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Security Headers
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
```

#### Deploy Previews
- **Automático:** Para cada Pull Request
- **URL temporal:** Generada automáticamente
- **Testing:** Permite probar cambios antes del merge

### 2.3 Comandos de Despliegue

#### Despliegue Automático
```bash
# Push a main branch activa deployment automático
git add .
git commit -m "feat: nueva funcionalidad"
git push origin main
```

#### Despliegue Manual con CLI
```bash
# Instalar Netlify CLI
npm install -g netlify-cli

# Login
netlify login

# Build local
npm run build

# Deploy a staging
netlify deploy --dir=dist

# Deploy a producción
netlify deploy --dir=dist --prod
```

## 3. DESPLIEGUE EN VERCEL

### 3.1 Configuración Inicial

#### Paso 1: Preparación
```bash
# Verificar vercel.json en la raíz
ls -la vercel.json

# Instalar Vercel CLI (opcional)
npm install -g vercel
```

#### Paso 2: Crear Proyecto en Vercel
1. **Crear cuenta:** https://vercel.com
2. **Importar proyecto:** "New Project"
3. **Conectar GitHub:** Seleccionar repositorio
4. **Configuración automática:** Vercel detecta Vite
5. **Deploy:** Primer deployment automático

#### Paso 3: Configurar Variables de Entorno
```bash
# En Vercel Dashboard → Settings → Environment Variables
VITE_EMAILJS_SERVICE_ID=service_xxxxxxx
VITE_EMAILJS_TEMPLATE_ID=template_xxxxxxx
VITE_EMAILJS_PUBLIC_KEY=xxxxxxxxxxxxxxx
```

### 3.2 Comandos de Despliegue

#### Despliegue con CLI
```bash
# Login
vercel login

# Deploy a preview
vercel

# Deploy a producción
vercel --prod
```

#### Despliegue Automático
- **Trigger:** Push a main branch
- **Preview:** Para branches y PRs
- **Producción:** Solo main branch

## 4. DESPLIEGUE EN GITHUB PAGES

### 4.1 Configuración

#### GitHub Actions Workflow
```yaml
# .github/workflows/deploy-gh-pages.yml
name: Deploy to GitHub Pages

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Build
        run: npm run build
        env:
          VITE_EMAILJS_SERVICE_ID: ${{ secrets.VITE_EMAILJS_SERVICE_ID }}
          VITE_EMAILJS_TEMPLATE_ID: ${{ secrets.VITE_EMAILJS_TEMPLATE_ID }}
          VITE_EMAILJS_PUBLIC_KEY: ${{ secrets.VITE_EMAILJS_PUBLIC_KEY }}
      
      - name: Deploy to GitHub Pages
        uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./dist
```

#### Configuración de Vite para GitHub Pages
```typescript
// vite.config.ts - Agregar base para GitHub Pages
export default defineConfig({
  base: '/spa-pascale/', // Nombre del repositorio
  // ... resto de configuración
});
```

## 5. HOSTING TRADICIONAL

### 5.1 Preparación para Hosting Compartido

#### Build para Hosting Tradicional
```bash
# Build de producción
npm run build

# Comprimir archivos
zip -r spa-pascale-dist.zip dist/

# Subir vía FTP/cPanel
# Extraer en directorio público del hosting
```

#### Configuración de .htaccess
```apache
# .htaccess para SPA routing
RewriteEngine On
RewriteBase /

# Handle Angular and other client-side routing
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule . /index.html [L]

# Security Headers
Header always set X-Frame-Options DENY
Header always set X-Content-Type-Options nosniff
Header always set X-XSS-Protection "1; mode=block"
Header always set Referrer-Policy "strict-origin-when-cross-origin"

# Cache Control
<filesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg)$">
  Header set Cache-Control "max-age=********, public, immutable"
</filesMatch>
```

## 6. PROCESO DE DESPLIEGUE

### 6.1 Pre-Despliegue Checklist

#### Verificaciones Técnicas
- [ ] `npm run type-check` sin errores
- [ ] `npm run lint` sin warnings
- [ ] `npm run build` exitoso
- [ ] `npm run preview` funcional
- [ ] Variables de entorno configuradas
- [ ] Tests manuales completados

#### Verificaciones de Contenido
- [ ] Información de contacto actualizada
- [ ] Imágenes optimizadas y funcionando
- [ ] Textos revisados y sin errores
- [ ] Links externos funcionando
- [ ] Formularios probados

#### Verificaciones de SEO
- [ ] Meta tags actualizados
- [ ] Sitemap.xml generado
- [ ] Robots.txt configurado
- [ ] URLs amigables
- [ ] Structured data implementado

### 6.2 Proceso de Despliegue Paso a Paso

#### Despliegue Estándar
1. **Preparación:**
   ```bash
   git checkout main
   git pull origin main
   npm install
   ```

2. **Verificación:**
   ```bash
   npm run type-check
   npm run lint
   npm run build
   npm run preview
   ```

3. **Despliegue:**
   ```bash
   # Automático: Push activa deployment
   git push origin main
   
   # Manual: CLI deployment
   netlify deploy --prod --dir=dist
   ```

4. **Verificación Post-Despliegue:**
   - [ ] Sitio carga correctamente
   - [ ] Navegación funcional
   - [ ] Formularios operativos
   - [ ] Performance aceptable
   - [ ] No errores en consola

### 6.3 Despliegue de Emergencia

#### Hotfix Deployment
```bash
# Crear branch de hotfix
git checkout -b hotfix/critical-fix

# Implementar fix
# ... cambios necesarios

# Commit y push
git add .
git commit -m "hotfix: critical security fix"
git push origin hotfix/critical-fix

# Merge directo a main (en emergencias)
git checkout main
git merge hotfix/critical-fix
git push origin main

# Deploy automático se activa
```

## 7. MONITOREO POST-DESPLIEGUE

### 7.1 Verificaciones Inmediatas

#### Health Checks (Primeros 5 minutos)
- [ ] **Disponibilidad:** Sitio responde correctamente
- [ ] **Performance:** Tiempo de carga <3 segundos
- [ ] **Funcionalidad:** Navegación y formularios operativos
- [ ] **Errores:** No errores JavaScript en consola
- [ ] **Mobile:** Responsive design funcionando

#### Verificaciones Extendidas (Primera hora)
- [ ] **SEO:** Meta tags y structured data
- [ ] **Analytics:** Tracking funcionando
- [ ] **Forms:** Envío de emails operativo
- [ ] **WhatsApp:** Integración funcional
- [ ] **Images:** Todas las imágenes cargan

### 7.2 Métricas de Despliegue

#### Métricas Actuales
- **Build Time:** 2-3 minutos
- **Deploy Time:** 30-60 segundos
- **Success Rate:** 100% (inicial)
- **Rollback Time:** <1 minuto

#### Objetivos de Performance
- **First Contentful Paint:** <1.5s
- **Largest Contentful Paint:** <2.5s
- **Cumulative Layout Shift:** <0.1
- **First Input Delay:** <100ms

## 8. ROLLBACK Y RECOVERY

### 8.1 Estrategias de Rollback

#### Rollback Automático (Netlify)
```bash
# Rollback al deployment anterior
netlify rollback

# Rollback a deployment específico
netlify rollback --site-id=SITE_ID --deploy-id=DEPLOY_ID
```

#### Rollback Manual (Git)
```bash
# Revert último commit
git revert HEAD
git push origin main

# Revert a commit específico
git revert COMMIT_HASH
git push origin main
```

### 8.2 Procedimiento de Recovery

#### Pasos de Recovery
1. **Detectar problema:** Monitoreo o reporte de usuario
2. **Evaluar impacto:** Severidad y alcance del issue
3. **Decidir estrategia:** Rollback vs hotfix
4. **Ejecutar recovery:** Según estrategia elegida
5. **Verificar solución:** Health checks completos
6. **Comunicar status:** Stakeholders y usuarios
7. **Post-mortem:** Análisis y mejoras

## 9. CONFIGURACIÓN DE DOMINIOS

### 9.1 Dominio Personalizado

#### Netlify Domain Setup
1. **Comprar dominio:** Registrar spapascale.com
2. **Configurar DNS:** Apuntar a Netlify
3. **SSL automático:** Certificado Let's Encrypt
4. **Verificar configuración:** DNS propagation

#### DNS Configuration
```
# DNS Records para Netlify
Type: CNAME
Name: www
Value: spa-pascale.netlify.app

Type: A
Name: @
Value: 75.2.60.5 (Netlify Load Balancer)
```

### 9.2 Subdominios

#### Staging Subdomain
```
# staging.spapascale.com
Type: CNAME
Name: staging
Value: staging--spa-pascale.netlify.app
```

## 10. TROUBLESHOOTING

### 10.1 Problemas Comunes

#### Build Failures
```bash
# Error: Node version incompatible
# Solución: Verificar NODE_VERSION en variables de entorno

# Error: Dependencies not found
# Solución: Verificar package-lock.json está committeado

# Error: Environment variables missing
# Solución: Configurar variables en dashboard de hosting
```

#### Runtime Errors
```bash
# Error: 404 en rutas SPA
# Solución: Verificar redirects configurados

# Error: CORS en EmailJS
# Solución: Verificar dominio en configuración EmailJS

# Error: Images not loading
# Solución: Verificar URLs y lazy loading
```

### 10.2 Logs y Debugging

#### Netlify Logs
```bash
# Ver logs de build
netlify logs

# Ver logs de functions (futuro)
netlify logs --functions
```

#### Browser Debugging
- **Console:** Verificar errores JavaScript
- **Network:** Verificar requests fallidos
- **Performance:** Analizar métricas de carga
- **Application:** Verificar service workers (futuro)

---

**Última actualización:** 25 de Julio de 2025  
**Próxima revisión:** 1 de Agosto de 2025  
**Responsable de Deployment:** Agente de IA Senior
