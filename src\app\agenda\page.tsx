'use client';


import Script from 'next/script';

import React, { useState } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { CalendarIcon } from '../../components/icons/CalendarIcon';
import { LeafIcon } from '../../components/icons/LeafIcon';
import { TREATMENTS_DATA } from '../../constants';
import { useScrollAnimation } from '../../hooks/useScrollAnimation';



interface FormData {
  name: string;
  email: string;
  phone: string;
  service: string;
  notes: string;
}

// Mock business hours and appointment settings
const BUSINESS_HOURS = { start: 9, end: 21 };
const APPOINTMENT_DURATION_MINUTES = 60;
const APPOINTMENTS_PER_DAY = 5; // Max appointments per day for simulation

// Helper to format date as YYYY-MM-DD
const formatDateToISO = (date: Date): string => {
  return date.toISOString().split('T')[0];
};

export default function AgendaPage() {
  const searchParams = useSearchParams();
  const router = useRouter();

  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [selectedTime, setSelectedTime] = useState<string | null>(null);
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    phone: '',
    service: '',
    notes: '',
  });
  const [errors, setErrors] = useState<Partial<FormData>>({});
  const [bookingStep, setBookingStep] = useState<
    'date' | 'time' | 'details' | 'confirmation'
  >('date');

  const [sectionRef, isVisible] = useScrollAnimation<HTMLDivElement>({
    threshold: 0.05,
  });

  useEffect(() => {
    const selectedService = searchParams.get('service');
    if (selectedService) {
      setFormData(prev => ({ ...prev, service: selectedService }));
    }
  }, [searchParams]);

  const daysInMonth = useMemo(() => {
    const date = new Date(
      currentMonth.getFullYear(),
      currentMonth.getMonth(),
      1
    );
    const days: Array<{
      day: number;
      date: Date;
      isToday: boolean;
      isPast: boolean;
    }> = [];
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    while (date.getMonth() === currentMonth.getMonth()) {
      const currentDate = new Date(date);
      currentDate.setHours(0, 0, 0, 0);
      days.push({
        day: date.getDate(),
        date: new Date(date),
        isToday: formatDateToISO(date) === formatDateToISO(new Date()),
        isPast: currentDate < today,
      });
      date.setDate(date.getDate() + 1);
    }
    return days;
  }, [currentMonth]);

  const firstDayOfMonth = useMemo(() => {
    return new Date(
      currentMonth.getFullYear(),
      currentMonth.getMonth(),
      1
    ).getDay();
  }, [currentMonth]);

  const handlePrevMonth = () => {
    setCurrentMonth(
      prev => new Date(prev.getFullYear(), prev.getMonth() - 1, 1)
    );
    setSelectedDate(null);
    setSelectedTime(null);
    setBookingStep('date');
  };

  const handleNextMonth = () => {
    setCurrentMonth(
      prev => new Date(prev.getFullYear(), prev.getMonth() + 1, 1)
    );
    setSelectedDate(null);
    setSelectedTime(null);
    setBookingStep('date');
  };

  const handleDateSelect = (date: Date) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    if (date < today) return; // Prevent selecting past dates

    setSelectedDate(date);
    setSelectedTime(null);
    setBookingStep('time');
  };

  // Mock time slots generation
  const availableTimeSlots = useMemo(() => {
    if (!selectedDate) return [];
    const slots: { time: string; isBooked: boolean }[] = [];
    for (
      let hour = BUSINESS_HOURS.start;
      hour < BUSINESS_HOURS.end;
      hour += APPOINTMENT_DURATION_MINUTES / 60
    ) {
      const minutes = (hour % 1) * 60;
      const displayHour = Math.floor(hour);
      const timeString = `${String(displayHour).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`;
      // Simulate some slots being booked (pseudo-randomly based on date and time)
      const isBooked =
        (selectedDate.getDate() + displayHour) %
          Math.floor((24 / APPOINTMENTS_PER_DAY) * 1.5) ===
        0;
      slots.push({ time: timeString, isBooked });
    }
    return slots; // Display all generated slots within business hours
  }, [selectedDate]);

  const handleTimeSelect = (time: string) => {
    setSelectedTime(time);
    setBookingStep('details');
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<FormData> = {};
    if (!formData.name.trim()) newErrors.name = 'El nombre es obligatorio.';
    if (!formData.email.trim()) {
      newErrors.email = 'El correo electrónico es obligatorio.';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'El correo electrónico no es válido.';
    }
    if (formData.phone.trim() && !/^\+?[0-9\s-]{7,15}$/.test(formData.phone)) {
      newErrors.phone = 'El número de teléfono no es válido.';
    }
    if (!formData.service) newErrors.service = 'Selecciona un servicio.';
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
    if (errors[e.target.name as keyof FormData]) {
      setErrors({ ...errors, [e.target.name]: undefined });
    }
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!validateForm()) return;
    // TODO: Integrate with backend for actual booking
    // console.log('Booking submitted:', { ...formData, date: selectedDate, time: selectedTime });
    setBookingStep('confirmation');
  };

  const resetAgenda = () => {
    setSelectedDate(null);
    setSelectedTime(null);
    setFormData({
      name: '',
      email: '',
      phone: '',
      service: searchParams.get('service') || '',
      notes: '',
    });
    setErrors({});
    setBookingStep('date');
    router.push('/agenda'); // Clear state on reset
  };

  const renderCalendar = () => (
    <div className="bg-white p-6 rounded-lg shadow-lg">
      <div className="flex justify-between items-center mb-4">
        <button
          onClick={handlePrevMonth}
          className="px-3 py-1 bg-brand-primary text-white rounded hover:bg-brand-dark"
        >
          &lt;
        </button>
        <h3 className="text-xl font-semibold text-brand-dark">
          {currentMonth.toLocaleString('es-CL', {
            month: 'long',
            year: 'numeric',
          })}
        </h3>
        <button
          onClick={handleNextMonth}
          className="px-3 py-1 bg-brand-primary text-white rounded hover:bg-brand-dark"
        >
          &gt;
        </button>
      </div>
      <div className="grid grid-cols-7 gap-1 text-center text-sm text-brand-text-light mb-2">
        {['Dom', 'Lun', 'Mar', 'Mié', 'Jue', 'Vie', 'Sáb'].map(day => (
          <div key={day}>{day}</div>
        ))}
      </div>
      <div className="grid grid-cols-7 gap-1">
        {Array(firstDayOfMonth)
          .fill(null)
          .map((_, i) => (
            <div key={`empty-${i}`}></div>
          ))}
        {daysInMonth.map(({ day, date, isToday, isPast }) => (
          <button
            key={day}
            onClick={() => handleDateSelect(date)}
            disabled={isPast}
            className={`
              p-2 rounded hover:bg-brand-primary hover:text-white transition-colors
              ${isPast ? 'text-slate-300 cursor-not-allowed' : 'text-brand-text hover:bg-brand-primary/80'}
              ${isToday ? 'ring-2 ring-brand-secondary' : ''}
              ${selectedDate && formatDateToISO(selectedDate) === formatDateToISO(date) ? 'bg-brand-primary text-white' : 'bg-slate-100'}
            `}
          >
            {day}
          </button>
        ))}
      </div>
    </div>
  );

  const renderTimeSlots = () => (
    <div className="bg-white p-6 rounded-lg shadow-lg">
      <h3 className="text-xl font-semibold text-brand-dark mb-1">
        Horas para{' '}
        {selectedDate?.toLocaleDateString('es-CL', {
          weekday: 'long',
          day: 'numeric',
          month: 'long',
        })}
      </h3>
      <button
        onClick={() => setBookingStep('date')}
        className="text-sm text-brand-primary hover:underline mb-4 block"
      >
        Cambiar Fecha
      </button>
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3">
        {availableTimeSlots.map(({ time, isBooked }) => (
          <button
            key={time}
            onClick={() => handleTimeSelect(time)}
            disabled={isBooked}
            className={`
              p-3 rounded text-sm font-medium transition-all
              ${
                isBooked
                  ? 'bg-slate-200 text-slate-400 cursor-not-allowed line-through'
                  : 'bg-brand-primary text-white hover:bg-brand-dark hover:scale-105 focus:ring-2 focus:ring-brand-secondary'
              }
              ${selectedTime === time ? 'ring-2 ring-brand-secondary scale-105' : ''}
            `}
          >
            {time}
          </button>
        ))}
      </div>
      {availableTimeSlots.filter(slot => !slot.isBooked).length === 0 && (
        <p className="text-brand-text-light mt-4 text-center">
          No hay horas disponibles para este día. Por favor, selecciona otra
          fecha.
        </p>
      )}
    </div>
  );

  const renderDetailsForm = () => (
    <div className="bg-white p-6 sm:p-8 rounded-lg shadow-lg">
      <h3 className="text-xl font-semibold text-brand-dark mb-1">
        Confirma tus Datos
      </h3>
      <p className="text-brand-text-light mb-2 text-sm">
        Cita para:{' '}
        {selectedDate?.toLocaleDateString('es-CL', {
          weekday: 'long',
          day: 'numeric',
          month: 'long',
        })}{' '}
        a las {selectedTime}
      </p>
      <div className="mb-4">
        <button
          onClick={() => setBookingStep('time')}
          className="text-sm text-brand-primary hover:underline mr-4"
        >
          Cambiar Hora
        </button>
        <button
          onClick={() => setBookingStep('date')}
          className="text-sm text-brand-primary hover:underline"
        >
          Cambiar Fecha
        </button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4" noValidate>
        <div>
          <label
            htmlFor="name"
            className="block text-sm font-medium text-brand-text"
          >
            Nombre Completo
          </label>
          <input
            type="text"
            name="name"
            id="name"
            value={formData.name}
            onChange={handleChange}
            required
            aria-required="true"
            className={`mt-1 block w-full px-3 py-2 bg-slate-50 border rounded-md shadow-sm focus:outline-none focus:ring-brand-primary focus:border-brand-primary sm:text-sm ${errors.name ? 'border-red-500' : 'border-slate-300'}`}
          />
          {errors.name && (
            <p className="mt-1 text-xs text-red-600">{errors.name}</p>
          )}
        </div>
        <div>
          <label
            htmlFor="email"
            className="block text-sm font-medium text-brand-text"
          >
            Correo Electrónico
          </label>
          <input
            type="email"
            name="email"
            id="email"
            value={formData.email}
            onChange={handleChange}
            required
            aria-required="true"
            className={`mt-1 block w-full px-3 py-2 bg-slate-50 border rounded-md shadow-sm focus:outline-none focus:ring-brand-primary focus:border-brand-primary sm:text-sm ${errors.email ? 'border-red-500' : 'border-slate-300'}`}
          />
          {errors.email && (
            <p className="mt-1 text-xs text-red-600">{errors.email}</p>
          )}
        </div>
        <div>
          <label
            htmlFor="phone"
            className="block text-sm font-medium text-brand-text"
          >
            Teléfono (Opcional)
          </label>
          <input
            type="tel"
            name="phone"
            id="phone"
            value={formData.phone}
            onChange={handleChange}
            className={`mt-1 block w-full px-3 py-2 bg-slate-50 border rounded-md shadow-sm focus:outline-none focus:ring-brand-primary focus:border-brand-primary sm:text-sm ${errors.phone ? 'border-red-500' : 'border-slate-300'}`}
          />
          {errors.phone && (
            <p className="mt-1 text-xs text-red-600">{errors.phone}</p>
          )}
        </div>
        <div>
          <label
            htmlFor="service"
            className="block text-sm font-medium text-brand-text"
          >
            Servicio
          </label>
          <select
            name="service"
            id="service"
            value={formData.service}
            onChange={handleChange}
            required
            aria-required="true"
            className={`mt-1 block w-full px-3 py-2 bg-slate-50 border rounded-md shadow-sm focus:outline-none focus:ring-brand-primary focus:border-brand-primary sm:text-sm ${errors.service ? 'border-red-500' : 'border-slate-300'}`}
          >
            <option value="">Selecciona un servicio...</option>
            {TREATMENTS_DATA.map(treatment => (
              <option key={treatment.id} value={treatment.title}>
                {treatment.title}
              </option>
            ))}
            <option value="Consulta General">Consulta General</option>
          </select>
          {errors.service && (
            <p className="mt-1 text-xs text-red-600">{errors.service}</p>
          )}
        </div>
        <div>
          <label
            htmlFor="notes"
            className="block text-sm font-medium text-brand-text"
          >
            Notas Adicionales (Opcional)
          </label>
          <textarea
            name="notes"
            id="notes"
            rows={3}
            value={formData.notes}
            onChange={handleChange}
            className="mt-1 block w-full px-3 py-2 bg-slate-50 border border-slate-300 rounded-md shadow-sm focus:outline-none focus:ring-brand-primary focus:border-brand-primary sm:text-sm"
          ></textarea>
        </div>
        <button
          type="submit"
          className="w-full flex items-center justify-center px-6 py-3 bg-brand-primary hover:bg-brand-dark text-white font-semibold rounded-lg shadow-md hover:shadow-lg transition-all duration-300 transform hover:scale-105"
        >
          <CalendarIcon className="w-5 h-5 mr-2" />
          Confirmar Cita
        </button>
      </form>
    </div>
  );

  const renderConfirmation = () => (
    <div className="bg-white p-6 sm:p-8 rounded-lg shadow-lg text-center">
      <LeafIcon className="w-16 h-16 text-green-500 mx-auto mb-4" />
      <h3 className="text-2xl font-serif font-semibold text-brand-primary mb-3">
        ¡Cita Agendada Exitosamente!
      </h3>
      <p className="text-brand-text-light mb-2">
        Gracias,{' '}
        <span className="font-medium text-brand-dark">{formData.name}</span>.
      </p>
      <p className="text-brand-text-light mb-1">
        Hemos registrado tu solicitud para el servicio de{' '}
        <span className="font-medium text-brand-dark">{formData.service}</span>.
      </p>
      <p className="text-brand-text-light mb-4">
        Fecha y Hora:{' '}
        <span className="font-medium text-brand-dark">
          {selectedDate?.toLocaleDateString('es-CL')} a las {selectedTime}
        </span>
        .
      </p>
      <p className="text-sm text-brand-text-light mb-6">
        Recibirás una confirmación por correo electrónico.
      </p>
      <button
        onClick={resetAgenda}
        className="px-6 py-3 bg-brand-primary hover:bg-brand-dark text-white font-semibold rounded-lg shadow-md hover:shadow-lg transition-transform duration-300 transform hover:scale-105"
      >
        Agendar Otra Cita
      </button>
    </div>
  );

  return (
    <>
      <Script
        id="booking-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify({
          "@context": "https://schema.org",
          "@type": "Service",
          "name": "Reserva de Cita en SPA Pascale",
          "description": "Permite a los usuarios agendar citas para masajes y terapias de bienestar en SPA Pascale.",
          "provider": {
            "@type": "Organization",
            "name": "SPA Pascale"
          },
          "serviceType": "Health and Beauty Appointment",
          "areaServed": {
            "@type": "Place",
            "name": "Santiago, Chile"
          },
          "potentialAction": {
            "@type": "BookAction",
            "target": "https://www.spapascale.com/agenda", // Reemplazar con tu URL real
            "actionPlatform": [
              "http://schema.org/DesktopWebPlatform",
              "http://schema.org/MobileWebPlatform"
            ]
          }
        }) }}
      />
      <div
        ref={sectionRef}
        className={`py-12 sm:py-20 bg-brand-light transition-all duration-700 ease-out transform ${
          isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}
      >
        <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-10">
            <h1 className="text-3xl font-serif font-bold text-brand-primary sm:text-4xl">
              Agenda tu Cita
            </h1>
            <p className="mt-3 text-md text-brand-text-light max-w-xl mx-auto">
              Selecciona una fecha y hora para tu próxima sesión de bienestar.
            </p>
          </div>

          <div className="space-y-8">
            {bookingStep === 'date' && renderCalendar()}
            {bookingStep === 'time' && selectedDate && renderTimeSlots()}
            {bookingStep === 'details' &&
              selectedDate &&
              selectedTime &&
              renderDetailsForm()}
            {bookingStep === 'confirmation' && renderConfirmation()}
          </div>
        </div>
      </div>
    </>
  );
}