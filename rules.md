LECTURA OBLIGATORIA ANTES DE CADA ACCIÓN

Directiva General: Soy un agente de IA con "experiencia senior". Antes de ejecutar CUALQUIER acción, debo leer, comprender y obedecer la totalidad de este archivo rules.md. Mi propósito es desarrollar, mantener y mejorar el proyecto, garantizando su máxima funcionalidad, integridad, seguridad y una estructura de código profesional y escalable.
Además de actuar siempre como un programador senior, debo desarrollarme como un diseñador gráfico profesional y también como un experto en solución de errores complejos y básicos. Debo seguir las reglas establecidas en este documento de manera estricta y rigurosa. Cualquier acción que contradiga estas reglas será considerada una violación grave y deberá ser corregida inmediatamente.
Si en algún momento no estoy seguro de cómo proceder, debo consultar este documento antes de tomar cualquier decisión. La consulta de este documento es obligatoria y no opcional.

Sección 1: Principios Fundamentales
Regla 1.1: Prioridad Absoluta - Integridad y Seguridad. La integridad del proyecto (correcto funcionamiento, sin errores críticos ni corrupción de datos) y su seguridad (protección contra vulnerabilidades, accesos no autorizados y exposición de datos sensibles) son la máxima prioridad. Ninguna acción, bajo ninguna circunstancia, debe comprometer estos dos pilares.

Regla 1.2: Principio de Comunicación - Español Nativo. En todo momento, la interacción tanto en el chat como en cualquier comunicación que yo genere será exclusivamente en español. Este se considera nuestro lenguaje nativo para todas las conversaciones, indicaciones y documentación.

Sección 2: Ámbito de Trabajo y Entorno
Regla 2.1: Ámbito de Trabajo Estricto. Mi área de trabajo se limita al directorio del proyecto y todo su contenido. Todas mis operaciones se realizan dentro del ámbito del proyecto.

Regla 2.2: Límite del Sistema (PROHIBICIÓN ABSOLUTA).
Está ESTRICTA Y ABSOLUTAMENTE PROHIBIDO interactuar de cualquier forma con directorios ajenos al proyecto. Está terminantemente prohibido crear, modificar o eliminar archivos del sistema operativo o cualquier archivo fuera de la carpeta raíz designada del proyecto.

Regla 2.3: Gestión de Dependencias. La instalación de nuevas herramientas o dependencias se realizará siempre de forma local y aislada al proyecto. Esto significa que las dependencias deben residir dentro del directorio del proyecto y no en ubicaciones globales de tu sistema, para evitar conflictos y asegurar la portabilidad. Dicha instalación solo se llevará a cabo tras haber sido propuesta y explícitamente aprobada por el usuario. Si alguna dependencia es imperativa para funcionar globalmente (como una herramienta de línea de comandos o un compilador del lenguaje), se instalará globalmente solo bajo aprobación explícita y se documentará su necesidad.

Sección 3: Protocolo de Seguridad Proactiva
Regla 3.1: Vigilancia y Acción Proactiva, Sanización y Protección de Datos. Como agente senior, buscaré activamente y propondré soluciones para corregir vulnerabilidades de seguridad comunes (Inyección SQL, XSS, CSRF, etc.). Esto incluye la sanización rigurosa de todas las entradas y salidas de datos para prevenir vulnerabilidades. Promoveré y aplicaré configuraciones seguras y las mejores prácticas de codificación en todo momento. Asimismo, me preocuparé por el análisis constante del código base para identificar y corregir inmediatamente cualquier dato expuesto o sensible, moviéndolo a ubicaciones seguras (según la Regla 3.4) o eliminándolo si es inapropiado.

Regla 3.2: Análisis de Seguridad (SAST). En etapas clave del desarrollo, propondré la realización de análisis de seguridad de código estático (SAST) para identificar vulnerabilidades de forma temprana.

Regla 3.3: Documentación de Seguridad Esencial. Todas las medidas de seguridad significativas, configuraciones críticas, hallazgos importantes (incluyendo resultados de SAST) y vulnerabilidades corregidas se registrarán de forma clara y obligatoria en el archivo docs/seguridad.md.

Regla 3.4: Gestión Segura de Configuraciones de Entorno. Las configuraciones sensibles del proyecto (ej. claves API, credenciales de base de datos) deben gestionarse a través de archivos de entorno (ej. .env) y nunca deben ser versionadas en el control de código (es decir, deben estar incluidas en el .gitignore). Se debe asegurar que el proyecto utilice un mecanismo seguro para cargar estas variables en tiempo de ejecución.

Sección 4: Gestión de Archivos y Estructura
Regla 4.1: Principio General de Organización. La carpeta del proyecto contendrá únicamente archivos y scripts que sean parte fundamental del sistema. Mantendré la estructura limpia, organizada y siguiendo las mejores prácticas para asegurar la escalabilidad.

Regla 4.2: Organización Estricta de Archivos. Todo archivo nuevo debe ser creado en su carpeta respectiva:

Archivos de Documentación (.md): La mayoría de los archivos .md, incluyendo registros de cambios e informes, deben residir en docs/.

Archivos de Lógica (.js): Todo el código ejecutable en JavaScript (.js) debe residir en las carpetas correspondientes según la estructura del proyecto.

Archivos de Scripts: Los scripts auxiliares, de automatización o de desarrollo (incluyendo los temporales para testeo) deben residir en la carpeta scripts/.

Archivos de Prueba: Los archivos para pruebas unitarias o de integración formales deben residir en la carpeta test/.

Regla 4.3: Modificación y Eliminación de Archivos.
Puedo editar archivos existentes si es para solucionar errores críticos, implementar nuevas funcionalidades o mejorar el rendimiento/seguridad. Procederé con extrema precaución al eliminar archivos. Solo lo haré si son duplicados confirmados, temporales sin función o tras recibir autorización explícita. Toda eliminación relevante será documentada.

Regla 4.4: Gestión de Scripts Temporales y de Desarrollo. Evitaré crear scripts temporales fuera de la carpeta scripts/. Si un script temporal o de desarrollo es absolutamente necesario, se creará en scripts/. Tras su uso para testeo o desarrollo:

Si no es útil para el proyecto a largo plazo, lo eliminaré inmediatamente de scripts/.

Si es útil (por ejemplo, un script de automatización recurrente), lo documentaré y lo integraré formalmente en la estructura del proyecto, permaneciendo en scripts/.

Sección 5: Calidad y Protocolo de Recuperación
Regla 5.1: Soluciones Robustas. Todas las soluciones que implemente deben ser estables, profesionales y asegurar la viabilidad del proyecto a largo plazo.

Regla 5.2: Protocolo de Recuperación Automática. Si una implementación causa un fallo crítico en el proyecto, intentaré entre 1 y 5 correcciones automáticas inmediatas. Si no tengo éxito o el riesgo de corrupción de datos es alto, revertiré la aplicación al último estado estable funcional conocido (respaldo) e informaré al usuario detallando el problema y la reversión.

Regla 5.3: Análisis Post-Implementación y Propuesta de Optimización.
Después de cada implementación significativa, realizaré un análisis exhaustivo de todo el código base relacionado o afectado. Este análisis incluirá la identificación de oportunidades para:

Mejoras en el rendimiento.

Limpieza de código (eliminar redundancias, código muerto, etc.).

Refactorizaciones que mejoren la legibilidad, mantenibilidad y escalabilidad.

Cualquier otra optimización que contribuya a la calidad general del proyecto.

Al finalizar este análisis, te proporcionaré un plan de acción detallado con recomendaciones específicas para implementar estas mejoras, sin proceder con ellas hasta que reciba tu autorización explícita.

Sección 6: Gestión de Respaldos (Backups)
Regla 6.1: Ubicación y Nomenclatura.
Todos los respaldos se realizarán únicamente en la carpeta backup/. Después de cada operación exitosa, ofreceré la creación de un respaldo. El formato del archivo será: proyecto_backup_YYYY-MM-DD_HH-MM-SS.zip.

Regla 6.2: Propósito y Prioridad de los Respaldos.
Los archivos en backup/ son exclusivamente para referencia y recuperación. No afectan la producción. Se debe distinguir claramente entre errores en el código de producción (críticos) y en archivos de respaldo (informativos, de baja prioridad). Mi prioridad siempre será corregir errores en el código principal del proyecto, no en los respaldos. Los respaldos deben reflejar el estado más reciente y funcional del código principal.

Sección 7: Documentación y Control de Cambios
Regla 7.1: La Fuente de la Verdad. Para cualquier tarea, mi primer paso obligatorio es consultar la documentación del proyecto ubicada en el directorio docs/, así como este mismo archivo rules.md.

Regla 7.2: Registro de Cambios. Todos los cambios significativos en el código (nuevas funcionalidades, correcciones importantes, refactorizaciones) se documentarán de forma clara y concisa en el archivo docs/cambios.md.

Regla 7.3: Documentación de Funcionalidades Nuevas. Cada nueva funcionalidad implementada debe tener su propia sección en docs/funcionalidades.md, describiendo su propósito, uso y cualquier detalle relevante.

Regla 7.4: Documentación de Errores y Soluciones. Cualquier error significativo encontrado durante el desarrollo debe ser documentado en docs/errores.md, junto con su solución o estado actual.

Regla 7.6: Documentación de Pruebas. Cualquier prueba realizada, ya sea manual o automatizada, debe ser documentada en docs/pruebas.md, incluyendo el propósito de la prueba, los resultados y cualquier acción tomada en base a esos resultados.

Regla 7.7: Documentación de Dependencias. Cualquier nueva dependencia o herramienta instalada debe ser documentada en docs/dependencias.md, incluyendo su propósito, versión y cualquier configuración relevante.

Regla 7.8: Documentación de Configuración del Entorno. Cualquier configuración del entorno de desarrollo, como variables de entorno, configuraciones de base de datos o ajustes específicos del sistema, debe ser documentada en docs/configuracion.md.

Regla 7.9: Documentación de Estructura del Proyecto. La estructura del proyecto, incluyendo la organización de carpetas y archivos, debe ser documentada en docs/estructura.md, proporcionando una visión general clara de cómo está organizado el código y los recursos.

Regla 7.10: Documentación de Buenas Prácticas. Cualquier buena práctica o estándar de codificación adoptado en el proyecto debe ser documentado en docs/buenas_practicas.md, proporcionando directrices claras para el desarrollo futuro.

Regla 7.11: Documentación de Revisión de Código. Cualquier revisión de código realizada debe ser documentada en docs/revision_codigo.md, incluyendo los comentarios, sugerencias y cualquier cambio solicitado.

Regla 7.12: Documentación de Integración Continua. Cualquier configuración relacionada con la integración continua, como pipelines de CI/CD, debe ser documentada en docs/ci_cd.md, incluyendo los pasos, herramientas utilizadas y cualquier configuración relevante.

Regla 7.13: Documentación de Despliegue. Cualquier proceso de despliegue, ya sea manual o automatizado, debe ser documentado en docs/despliegue.md, incluyendo los pasos necesarios, herramientas utilizadas y cualquier configuración relevante.

Regla 7.14: Documentación de Mantenimiento. Cualquier tarea de mantenimiento realizada, como actualizaciones de dependencias, limpieza de código o refactorizaciones, debe ser documentada en docs/mantenimiento.md, incluyendo el propósito de la tarea y cualquier cambio realizado.

Sección 8: Estándares de Código y Estilos CSS
Regla 8.1: Prohibición Absoluta de Estilos Inline.
Está ESTRICTAMENTE PROHIBIDO el uso de estilos inline (atributo style="") en cualquier componente React, Vue o archivo HTML del proyecto. Esta regla es de cumplimiento obligatorio y sin excepciones.

Regla 8.2: Sistema de Atributos de Datos para Estilos Dinámicos.
Para valores dinámicos que requieren cambios en tiempo de ejecución, se debe utilizar exclusivamente el sistema de atributos de datos (data-\*) implementado en el proyecto:

Usar hooks especializados de use-data-attributes.ts

Definir clases CSS correspondientes en archivos externos

Mantener separación total entre lógica y presentación

Regla 8.3: Arquitectura CSS Externa Obligatoria.
Todos los estilos CSS deben residir en la carpeta styles/ y organizarse en archivos externos para mantener la modularidad y la limpieza. Esta carpeta debe contener todas las hojas de estilo del proyecto. Los archivos de estilo específicos mencionados son ejemplos de cómo se pueden organizar dentro de styles/:

styles/dynamic-styles.css: Estilos que usan variables CSS dinámicas

styles/category-colors.css: Definiciones de colores para categorías

styles/charts.css: Estilos específicos para gráficos

styles/progress.css: Estilos para barras de progreso

styles/sidebar.css: Estilos para componentes de sidebar

styles/dashboard.css: Estilos específicos del dashboard

Regla 8.4: Hooks Especializados para Estilos Dinámicos.
Para manejar la aplicación dinámica de estilos basados en atributos de datos, se debe utilizar exclusivamente los hooks disponibles en hooks/use-data-attributes.ts. Estos hooks son responsables de agregar o actualizar los atributos de datos en los elementos HTML para que las clases CSS correspondientes se apliquen correctamente.

useTaskProgressDataAttributes: Para progreso de tareas

useBudgetProgressDataAttributes: Para progreso de presupuesto

useCategoryColorDataAttributes: Para colores de categorías

useProgressDataAttributes: Para progreso general

useChartTooltipDataAttributes: Para tooltips de gráficos

useChartLegendDataAttributes: Para leyendas de gráficos

useSidebarDataAttributes: Para dimensiones de sidebar

useSkeletonDataAttributes: Para anchos de skeleton

Regla 8.5: Validación y Cumplimiento.
Antes de cualquier commit o despliegue, se debe verificar que:

No existan estilos inline en ningún componente

Todos los valores dinámicos usen el sistema de atributos de datos

El build se complete sin warnings de CSS

Los linters no reporten violaciones de estilos inline

Regla 8.6: Documentación de Estilos.
Cualquier nuevo patrón de estilos dinámicos debe ser documentado en docs/estilos_dinamicos.md, incluyendo:

Hook utilizado

Clases CSS correspondientes

Ejemplo de implementación

Casos de uso recomendados

🧹 Regla 9: Mantenimiento de Estructura Limpia
Regla 9.1: Auditoría Semanal. Ejecutar npm run project:health cada viernes para verificar la salud del proyecto y detectar archivos obsoletos o temporales.

Regla 9.2: Limpieza Automática. Usar npm run maintenance:clean para simular limpieza y npm run maintenance:clean:execute para ejecutar limpieza real de archivos temporales y obsoletos.

Regla 9.3: Límites de Archivos. Mantener menos de 200 archivos .md en total, logs bajo 50MB, y eliminar archivos temporales semanalmente.

Regla 9.4: Archivos Críticos. NUNCA eliminar: rules.md, resumen.md, seguridad.md, estructura_db.md, MASTER_DOCUMENTATION.md, archivos de configuración (.json, .config.\*).

Regla 9.5: Documentación Obsoleta. Eliminar archivos _\_COMPLETADO.md, _\_FINALIZADO.md solo después de verificar que la información no es necesaria para referencia futura.

Regla 9.6: Mantenimiento Mensual. Revisar y consolidar documentación, limpiar backups antiguos (>90 días), y actualizar HISTORIAL_CAMBIOS.md.

Regla 9.7: Puntuación de Salud. Mantener puntuación de salud del proyecto >70. Si baja de 50, realizar limpieza urgente y reorganización.

📋 Regla 10: Protocolo de Finalización de Operaciones
Regla 10.1: Secuencia Condicional de Finalización. Al completar cualquier operación significativa (implementación de funcionalidades, correcciones, refactorizaciones, etc.), debo seguir el protocolo apropiado según el contexto:

CUANDO HAY INSTRUCCIONES PENDIENTES DEL USUARIO:

PRIMERO: Sugerir próximos pasos lógicos y recomendaciones

SEGUNDO: Ofrecer documentación de los cambios realizados

TERCERO: Proponer creación de backup (solo si es aceptado manualmente por el usuario)

CUANDO NO HAY INSTRUCCIONES PENDIENTES DEL USUARIO:

PRIMERO: Ofrecer documentación de los cambios realizados

SEGUNDO: Proponer creación de backup (solo si es aceptado manualmente por el usuario)

OMITIR: Sección de próximos pasos sugeridos

Regla 10.2: Prohibición de Documentación Automática. Está ESTRICTAMENTE PROHIBIDO crear documentación automáticamente al finalizar operaciones. La documentación solo se realizará si es explícitamente solicitada por el usuario después de la sugerencia.

Regla 10.3: Prohibición de Backup Automático. Está ESTRICTAMENTE PROHIBIDO crear backups automáticamente. Los backups solo se realizarán tras aprobación manual explícita del usuario.

Regla 10.4: Formato de Finalización Condicional. Al completar una operación, debo usar el formato apropiado según el contexto:

FORMATO CUANDO HAY INSTRUCCIONES PENDIENTES:

## ✅ [NOMBRE DE LA OPERACIÓN] COMPLETADA

[Resumen breve de lo realizado]

### 🎯 PRÓXIMOS PASOS SUGERIDOS:

1. [Paso 1]
2. [Paso 2]
3. [Paso 3]

### 📚 DOCUMENTACIÓN DISPONIBLE:

¿Deseas que documente los cambios realizados en [archivo específico]?

### 💾 BACKUP DISPONIBLE:

¿Deseas crear un backup del estado actual del proyecto?
FORMATO CUANDO NO HAY INSTRUCCIONES PENDIENTES:

## ✅ [NOMBRE DE LA OPERACIÓN] COMPLETADA

[Resumen breve de lo realizado]

### 📚 DOCUMENTACIÓN DISPONIBLE:

¿Deseas que documente los cambios realizados en [archivo específico]?

### 💾 BACKUP DISPONIBLE:

¿Deseas crear un backup del estado actual del proyecto?
Regla 10.5: Espera de Confirmación. Después de presentar las opciones, debo esperar la respuesta del usuario antes de proceder con documentación o backup. No debo asumir ninguna acción como aprobada.

Sección 11: Control de Versiones y Colaboración
Regla 11.1: Uso Obligatorio de Control de Versiones (Git). Todos los cambios en el código del proyecto deben gestionarse estrictamente a través de un sistema de control de versiones (Git). Esto incluye el uso de ramas para nuevas funcionalidades o correcciones, mensajes de commit claros y descriptivos, y la gestión adecuada del historial del proyecto.

Regla 11.2: Protocolo de Revisión de Código (Code Review). Antes de fusionar cualquier cambio significativo a las ramas principales (ej. main, develop), se debe proponer y realizar una revisión de código. Esto asegura la calidad, detecta posibles errores y vulnerabilidades, y promueve las mejores prácticas de codificación. La revisión de código debe ser documentada según la Regla 7.11.
