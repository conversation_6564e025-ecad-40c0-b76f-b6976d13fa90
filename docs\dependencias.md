# DOCUMENTACIÓN DE DEPENDENCIAS - SPA PASCALE

## Información del Documento
**Proyecto:** SPA Pascale - Bienestar Integral  
**Fecha de creación:** 25 de Julio de 2025  
**Versión:** 1.0  
**Responsable:** Agente de IA Senior  

---

## 1. DEPENDENCIAS DE PRODUCCIÓN

### 1.1 Framework Principal

#### React 19.1.0
- **Propósito:** Framework principal para la interfaz de usuario
- **Versión:** 19.1.0 (Latest)
- **Configuración:** Modo estricto habilitado
- **Justificación:** Framework maduro y estable para SPAs
- **Características utilizadas:**
  - Functional Components
  - Hooks (useState, useEffect, useRef)
  - Context API (preparado para uso futuro)
  - Concurrent Features

#### React DOM 19.1.0
- **Propósito:** Renderizado de React en el DOM
- **Versión:** 19.1.0 (Compatible con React 19)
- **Configuración:** Renderizado en modo concurrente
- **Uso:** Punto de entrada principal en `index.tsx`

### 1.2 Routing y Navegación

#### React Router DOM 7.7.1
- **Propósito:** Sistema de routing para SPA
- **Versión:** 7.7.1 (Latest stable - Actualizada 25/07/2025)
- **Configuración:** HashRouter para compatibilidad universal
- **Justificación:** Routing client-side sin configuración de servidor
- **Características utilizadas:**
  - HashRouter para compatibilidad con hosting estático
  - Routes y Route para definición de rutas
  - Navegación programática
  - Parámetros de ruta (preparado para uso futuro)

### 1.3 Servicios de Comunicación

#### @emailjs/browser 4.4.1
- **Propósito:** Envío de emails sin backend
- **Versión:** 4.4.1 (Latest)
- **Configuración:** Inicialización con claves públicas
- **Justificación:** Solución serverless para formularios de contacto
- **Variables de entorno requeridas:**
  - `VITE_EMAILJS_SERVICE_ID`
  - `VITE_EMAILJS_TEMPLATE_ID`
  - `VITE_EMAILJS_PUBLIC_KEY`
- **Fallback:** Sistema mailto implementado
- **Uso en proyecto:**
  - Formulario de contacto general
  - Sistema de reservas de citas
  - Notificaciones automáticas

## 2. DEPENDENCIAS DE DESARROLLO

### 2.1 Build Tools

#### Vite 6.2.0
- **Propósito:** Build tool y servidor de desarrollo
- **Versión:** 6.2.0 (Latest)
- **Configuración:** Optimizada para React y TypeScript
- **Justificación:** Build rápido y HMR eficiente
- **Características utilizadas:**
  - Hot Module Replacement (HMR)
  - Code splitting automático
  - Optimización de assets
  - Configuración personalizada en `vite.config.ts`

**Configuración específica:**
```typescript
// Optimizaciones implementadas
- Target: ES2015
- Minificación: Terser
- Code splitting: React y Router separados
- Eliminación de console.logs en producción
```

### 2.2 TypeScript y Tipado

#### TypeScript 5.7.2
- **Propósito:** Tipado estático para JavaScript
- **Versión:** 5.7.2 (Latest stable)
- **Configuración:** Modo estricto habilitado
- **Justificación:** Type safety y mejor experiencia de desarrollo
- **Características utilizadas:**
  - Strict mode
  - Interface definitions
  - Generic types
  - Union types

#### @types/react 19.1.8
- **Propósito:** Definiciones de tipos para React
- **Versión:** 19.1.8 (Compatible con React 19)
- **Uso:** Tipado de componentes y hooks

#### @types/react-dom 19.1.6
- **Propósito:** Definiciones de tipos para React DOM
- **Versión:** 19.1.6 (Compatible con React DOM 19)
- **Uso:** Tipado de métodos de renderizado

#### @types/node 22.16.5
- **Propósito:** Definiciones de tipos para Node.js
- **Versión:** 22.16.5 (Latest LTS - Actualizada 25/07/2025)
- **Uso:** Tipado para scripts de build y configuración

### 2.3 Linting y Formateo

#### ESLint 9.30.0
- **Propósito:** Análisis estático de código
- **Versión:** 9.30.0 (Actualizada 25/07/2025)
- **Configuración:** Configuración personalizada en `eslint.config.js`
- **Reglas implementadas:**
  - TypeScript strict rules
  - React best practices
  - Accessibility rules
  - Performance optimizations

#### TypeScript ESLint 8.38.0
- **Propósito:** Reglas de ESLint específicas para TypeScript
- **Versión:** 8.38.0 (Latest - Actualizada 25/07/2025)
- **Configuración:** Integrado con configuración principal
- **Características:**
  - Type-aware linting
  - TypeScript-specific rules
  - Performance optimizations

#### ESLint Plugin React Refresh 0.4.20
- **Propósito:** Reglas para React Fast Refresh
- **Versión:** 0.4.20 (Latest)
- **Uso:** Optimización de HMR en desarrollo

#### Prettier 3.6.2
- **Propósito:** Formateo automático de código
- **Versión:** 3.6.2 (Latest)
- **Configuración:** Integrado con ESLint
- **Archivos soportados:**
  - TypeScript/JavaScript (.ts, .tsx, .js, .jsx)
  - JSON (.json)
  - CSS (.css)
  - Markdown (.md)

### 2.4 Utilidades de Desarrollo

#### Globals 16.3.0
- **Propósito:** Definiciones de variables globales para ESLint
- **Versión:** 16.3.0 (Latest - Actualizada 25/07/2025)
- **Uso:** Configuración de entorno para linting

## 3. SCRIPTS DE PROYECTO

### 3.1 Scripts de Desarrollo
```json
"dev": "vite"                    // Servidor de desarrollo
"preview": "vite preview"        // Preview del build local
```

### 3.2 Scripts de Build
```json
"build": "vite build"            // Build de producción
"type-check": "tsc --noEmit"     // Verificación de tipos
```

### 3.3 Scripts de Calidad
```json
"lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0"
"lint:fix": "eslint . --ext ts,tsx --fix"
"format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,css,md}\" --ignore-path .prettierignore"
"format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,css,md}\" --ignore-path .prettierignore"
```

### 3.4 Scripts de Deployment
```json
"deploy:netlify": "npm run build && npx netlify deploy --prod --dir=dist"
"deploy:vercel": "npm run build && npx vercel --prod"
```

### 3.5 Scripts de Análisis
```json
"analyze": "npm run build && npx vite-bundle-analyzer dist/assets/*.js"
```

## 4. CONFIGURACIÓN DE ENTORNO

### 4.1 Variables de Entorno
```bash
# EmailJS Configuration (Opcional)
VITE_EMAILJS_SERVICE_ID=your_service_id
VITE_EMAILJS_TEMPLATE_ID=your_template_id
VITE_EMAILJS_PUBLIC_KEY=your_public_key

# Gemini API (Para funcionalidades futuras)
GEMINI_API_KEY=your_gemini_api_key
```

### 4.2 Archivos de Configuración

#### package.json
- **Tipo de módulo:** ESM (`"type": "module"`)
- **Versión:** 0.0.0 (desarrollo)
- **Privado:** true (no publicable)

#### tsconfig.json
- **Target:** ES2020
- **Module:** ESNext
- **Strict mode:** Habilitado
- **JSX:** react-jsx

#### vite.config.ts
- **Alias:** @ apunta a raíz del proyecto
- **Puerto desarrollo:** 3000
- **Puerto preview:** 4173
- **Optimizaciones:** Configuradas para producción

## 5. GESTIÓN DE DEPENDENCIAS

### 5.1 Política de Actualizaciones
- **Dependencias críticas:** Actualización mensual
- **Dependencias de desarrollo:** Actualización semanal
- **Verificación:** `npm audit` antes de cada deployment
- **Testing:** Pruebas completas después de actualizaciones

### 5.2 Compatibilidad
- **Node.js:** Versión 18 o superior requerida
- **npm:** Versión 8 o superior recomendada
- **Navegadores:** ES2015+ (95%+ de compatibilidad global)

### 5.3 Instalación Local
```bash
# Instalación completa
npm install

# Solo dependencias de producción
npm install --production

# Verificación de vulnerabilidades
npm audit

# Actualización de dependencias
npm update
```

## 6. DEPENDENCIAS FUTURAS PLANIFICADAS

### 6.1 Versión 1.1.0
- **React Query/TanStack Query:** Para gestión de estado servidor
- **Date-fns:** Para manipulación de fechas en sistema de citas
- **React Hook Form:** Para formularios más complejos

### 6.2 Versión 1.2.0
- **Workbox:** Para funcionalidades PWA
- **React Testing Library:** Para testing automatizado
- **Storybook:** Para documentación de componentes

## 7. HISTORIAL DE ACTUALIZACIONES

### 7.1 Auditoría de Dependencias - 25 de Julio de 2025

#### Actualizaciones Realizadas
- **@eslint/js:** 9.30.0 → 9.30.0 (Ya actualizada)
- **@types/node:** 22.15.33 → 22.16.5 (Patch update)
- **globals:** 16.2.0 → 16.3.0 (Minor update)
- **react-router-dom:** 7.6.3 → 7.7.1 (Minor update)
- **typescript-eslint:** 8.35.0 → 8.38.0 (Minor update)

#### Correcciones Implementadas
- **Tipos de Vite:** Creado `vite-env.d.ts` para tipos de `import.meta.env`
- **TypeScript Config:** Actualizado `tsconfig.json` para incluir tipos de Vite
- **ESLint:** Corregidos warnings de variables no utilizadas

#### Estado Post-Actualización
- **Vulnerabilidades:** 0 (Sin vulnerabilidades de seguridad)
- **Build:** ✅ Exitoso
- **Type Check:** ✅ Sin errores
- **Linting:** ✅ Solo warnings de console.log existentes (no críticos)

#### Dependencias Pendientes de Actualización Mayor
- **@types/node:** 22.16.5 → 24.1.0 (Major - Node.js 24 types)
- **typescript:** 5.7.3 → 5.8.3 (Minor pero significativo)
- **vite:** 6.3.5 → 7.0.6 (Major - Requiere testing exhaustivo)

---

**Última actualización:** 25 de Julio de 2025
**Próxima revisión:** 1 de Agosto de 2025
**Comando de verificación:** `npm audit && npm outdated`
