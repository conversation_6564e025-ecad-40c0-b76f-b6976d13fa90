import emailjs from '@emailjs/browser';

// Configuración de EmailJS (estas claves deben configurarse en el .env)
const EMAILJS_SERVICE_ID = process.env.NEXT_PUBLIC_EMAILJS_SERVICE_ID || 'service_6toa3zp';
const EMAILJS_TEMPLATE_ID = process.env.NEXT_PUBLIC_EMAILJS_TEMPLATE_ID || 'template_ab64d9x';
const EMAILJS_PUBLIC_KEY = process.env.NEXT_PUBLIC_EMAILJS_PUBLIC_KEY || 'ujcTML2vltdI0_D5t';

// Inicializar EmailJS
emailjs.init(EMAILJS_PUBLIC_KEY);

export interface ContactFormData {
  name: string;
  email: string;
  phone?: string;
  service: string;
  message: string;
}

export interface BookingFormData {
  name: string;
  email: string;
  phone?: string;
  service: string;
  date: string;
  time: string;
  notes?: string;
}

export interface EmailResponse {
  success: boolean;
  message: string;
  error?: string;
}

// Función para enviar email de contacto
export const sendContactEmail = async (formData: ContactFormData): Promise<EmailResponse> => {
  try {
    // Validar que las claves estén configuradas
    if (!EMAILJS_SERVICE_ID || !EMAILJS_TEMPLATE_ID || !EMAILJS_PUBLIC_KEY) {
      console.warn('EmailJS no está configurado. Usando fallback a mailto.');
      return sendMailtoFallback(formData);
    }

    const templateParams = {
      from_name: formData.name,
      from_email: formData.email,
      phone: formData.phone || 'No proporcionado',
      service: formData.service,
      message: formData.message,
      to_email: '<EMAIL>',
      subject: `Nueva consulta desde el sitio web - ${formData.service}`,
    };

    const response = await emailjs.send(
      EMAILJS_SERVICE_ID,
      EMAILJS_TEMPLATE_ID,
      templateParams
    );

    if (response.status === 200) {
      return {
        success: true,
        message: '¡Mensaje enviado exitosamente! Te contactaremos pronto.',
      };
    } else {
      throw new Error('Error en el envío');
    }
  } catch (error) {
    console.error('Error sending email:', error);
    
    // Fallback a mailto si EmailJS falla
    return sendMailtoFallback(formData);
  }
};

// Función para enviar email de reserva
export const sendBookingEmail = async (formData: BookingFormData): Promise<EmailResponse> => {
  try {
    if (!EMAILJS_SERVICE_ID || !EMAILJS_TEMPLATE_ID || !EMAILJS_PUBLIC_KEY) {
      console.warn('EmailJS no está configurado. Usando fallback a mailto.');
      return sendBookingMailtoFallback(formData);
    }

    const templateParams = {
      from_name: formData.name,
      from_email: formData.email,
      phone: formData.phone || 'No proporcionado',
      service: formData.service,
      booking_date: formData.date,
      booking_time: formData.time,
      notes: formData.notes || 'Sin notas adicionales',
      to_email: '<EMAIL>',
      subject: `Nueva reserva desde el sitio web - ${formData.service}`,
    };

    const response = await emailjs.send(
      EMAILJS_SERVICE_ID,
      EMAILJS_TEMPLATE_ID,
      templateParams
    );

    if (response.status === 200) {
      return {
        success: true,
        message: '¡Reserva enviada exitosamente! Te contactaremos para confirmar.',
      };
    } else {
      throw new Error('Error en el envío');
    }
  } catch (error) {
    console.error('Error sending booking email:', error);
    
    // Fallback a mailto si EmailJS falla
    return sendBookingMailtoFallback(formData);
  }
};

// Fallback usando mailto para contacto
const sendMailtoFallback = (formData: ContactFormData): EmailResponse => {
  const subject = encodeURIComponent(`Consulta desde el Sitio Web - ${formData.service}`);
  const body = encodeURIComponent(
    `Hola Carolina,

Una nueva consulta ha sido enviada a través del sitio web:

Nombre: ${formData.name}
Correo Electrónico: ${formData.email}
Teléfono: ${formData.phone || 'No proporcionado'}
Servicio de Interés: ${formData.service}

Mensaje:
${formData.message}

Saludos,
${formData.name}`
  );

  const mailtoLink = `mailto:<EMAIL>?subject=${subject}&body=${body}`;
  window.location.href = mailtoLink;

  return {
    success: true,
    message: 'Se abrirá tu cliente de email para enviar el mensaje.',
  };
};

// Fallback usando mailto para reservas
const sendBookingMailtoFallback = (formData: BookingFormData): EmailResponse => {
  const subject = encodeURIComponent(`Nueva Reserva - ${formData.service}`);
  const body = encodeURIComponent(
    `Hola Carolina,

Nueva reserva desde el sitio web:

Nombre: ${formData.name}
Correo Electrónico: ${formData.email}
Teléfono: ${formData.phone || 'No proporcionado'}
Servicio: ${formData.service}
Fecha: ${formData.date}
Hora: ${formData.time}
Notas: ${formData.notes || 'Sin notas adicionales'}

Por favor confirma la disponibilidad.

Saludos,
${formData.name}`
  );

  const mailtoLink = `mailto:<EMAIL>?subject=${subject}&body=${body}`;
  window.location.href = mailtoLink;

  return {
    success: true,
    message: 'Se abrirá tu cliente de email para enviar la reserva.',
  };
};

// Función para validar configuración de EmailJS
export const isEmailJSConfigured = (): boolean => {
  return !!(EMAILJS_SERVICE_ID && EMAILJS_TEMPLATE_ID && EMAILJS_PUBLIC_KEY &&
    EMAILJS_SERVICE_ID !== 'your_service_id' &&
    EMAILJS_TEMPLATE_ID !== 'your_template_id' &&
    EMAILJS_PUBLIC_KEY !== 'your_public_key');
};
