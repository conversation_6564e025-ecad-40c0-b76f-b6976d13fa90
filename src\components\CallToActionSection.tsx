'use client';

'use client';

import React from 'react';
import Link from 'next/link';
import { CALL_TO_ACTION_TEXT } from '../constants';
import { useScrollAnimation } from '../hooks/useScrollAnimation';
import { CalendarIcon } from './icons/CalendarIcon';

export const CallToActionSection: React.FC = () => {
  const [sectionRef, isVisible] = useScrollAnimation<HTMLElement>();

  return (
    <section
      ref={sectionRef}
      className={`py-16 sm:py-24 bg-brand-primary text-white transition-all duration-700 ease-out transform ${
        isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
      }`}
    >
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 className="text-3xl font-serif font-bold sm:text-4xl mb-6">
          {CALL_TO_ACTION_TEXT.main}
        </h2>
        <p className="text-lg font-light mb-8">{CALL_TO_ACTION_TEXT.button}</p>
        <Link
          href="/agenda"
          className="group relative inline-flex items-center justify-center px-8 py-4 bg-white hover:bg-slate-100 text-brand-primary font-semibold rounded-lg shadow-lg hover:shadow-xl text-lg transition-all duration-300 ease-in-out transform hover:scale-110 focus:outline-none focus:ring-4 focus:ring-brand-secondary/50 overflow-hidden"
          aria-label="Agendar una cita para servicios de bienestar"
        >
          {/* Subtle Inner Glow / Halo Effect */}
          <div
            className="absolute inset-0 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"
            style={{
              // Same halo effect as the treatment cards for consistency
              background:
                'radial-gradient(circle at center, rgba(13, 148, 136, 0) 0%, rgba(13, 148, 136, 0.05) 70%, rgba(13, 148, 136, 0.15) 100%)',
            }}
            aria-hidden="true"
          />

          {/* Button content needs a higher z-index to be on top of the glow */}
          <span className="relative z-10 flex items-center">
            <CalendarIcon className="w-5 h-5 mr-3" aria-hidden="true" />
            Agenda tu Cita
          </span>
        </Link>
      </div>
    </section>
  );
};
